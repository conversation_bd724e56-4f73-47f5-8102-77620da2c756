# 访问项目的根路径1
VITE_PUBLIC_PATH = /

# 代理设置，可配置多个，不能换行，格式：[访问接口的根路径, 代理地址, 是否保持Host头]
# VITE_PROXY = [["/js","https://vue.jeesite.com/js",true]]
# VITE_PROXY = [["/ASD","http://************:8981/ASD",false]]
VITE_PROXY = [["/ASD","http://************:8981/ASD",false]]
>>>>>>> Stashed changes
# VITE_PROXY = [["/M8","http://************:8980/M8",false]]

# 是否删除 console 调试信息
VITE_DROP_CONSOLE = false

# 是否启用 Mock 模拟测试
VITE_USE_MOCK = false

# 访问接口的根路径
VITE_GLOB_API_URL = 

# 访问接口的前缀，在根路径之后
VITE_GLOB_API_URL_PREFIX = /ASD

# 访问接口的管理基础路径
VITE_GLOB_ADMIN_PATH = /a

# 上传文件接口的根路径
VITE_GLOB_UPLOAD_URL = /upload

# 文件预览类型（true、oss）
VITE_FILE_PREVIEW = true
