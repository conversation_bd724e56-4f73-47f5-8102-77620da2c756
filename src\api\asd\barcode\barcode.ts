import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../model/baseModel';

const { adminPath } = useGlobSetting();

export interface BarCode extends BasicModel<BarCode> {
  cbarcode?: string;
}

export const BarCodeCheck = (params?: BarCode | any) =>
  defHttp.post<BarCode>({ url: adminPath + '/asd/barcode/analysisXpp', params });
