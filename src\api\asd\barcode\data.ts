import globalSetting from '/@/enums/globalSetting';

export interface GrowCardItem {
  icon: string;
  title: string;
  value: string;
  // total: number;
  // color: string;
  // action: string;
  type: string;
  auth: string;
}

export const growCardLists: GrowCardItem[] = [
  {
    title: '退料申请登记-原材料',
    icon: 'tui|svg',
    value: '材料退库申请登记-原材料',
    type: globalSetting.workScanEnum.TL,
    auth: 'asd:scan:tl',
  },
  {
    title: '退料申请登记-半成品',
    icon: 'tui|svg',
    value: '退库申请登记-半成品',
    type: globalSetting.workScanEnum.whTL,
    auth: 'asd:scan:tl',
  },
];
