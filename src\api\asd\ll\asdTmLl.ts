/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface AsdTmLl extends BasicModel<AsdTmLl> {
  djno?: string; // 申请单号
  cwhcode?: string; // 发料仓库
  xqcwhcode?: string; // 需求仓库
  cinvcode?: string; // 存货编码
  frate?: number; // 构成比例
  iqty?: number; // 需求数
  cposcode?: string; // 选定货位
  pickQty?: number; // 领料数
  pickBy?: string; // 领料人
  area?: string; // 区域
  xqDate?: string; // 需求时间
}

export const asdTmLlList = (params?: AsdTmLl | any) =>
  defHttp.get<AsdTmLl>({ url: adminPath + '/asd/ll/asdTmLl/list', params });

export const asdTmLlListData = (params?: AsdTmLl | any) =>
  defHttp.post<Page<AsdTmLl>>({ url: adminPath + '/asd/ll/asdTmLl/listData', params });

export const asdTmLOutListData = (params?: AsdTmLl | any) =>
  defHttp.post<Page<AsdTmLl>>({ url: adminPath + '/asd/ll/asdTmLl/findOutList', params });

export const asdTmLlForm = (params?: AsdTmLl | any) =>
  defHttp.get<AsdTmLl>({ url: adminPath + '/asd/ll/asdTmLl/form', params });

export const asdTmLlSave = (params?: any, data?: AsdTmLl | any) =>
  defHttp.postJson<AsdTmLl>({ url: adminPath + '/asd/ll/asdTmLl/save', params, data });

export const asdTmLlDelete = (params?: AsdTmLl | any) =>
  defHttp.get<AsdTmLl>({ url: adminPath + '/asd/ll/asdTmLl/delete', params });

export const fileDataUpload = (params?: AsdTmLl | any) =>
  defHttp.get<AsdTmLl>({
    url: adminPath + '/asd/ll/asdTmLl/fileDataUpload',
    params,
  });

export const fileOutDataUpload = (params?: AsdTmLl | any) =>
  defHttp.get<AsdTmLl>({
    url: adminPath + '/asd/ll/asdTmLl/fileOutDataUpload',
    params,
  });

export const asdTmLlHPosForm = (params?: AsdTmLl | any) =>
  defHttp.get<AsdTmLl>({ url: adminPath + '/asd/ll/asdTmLl/posForm', params });

export const handleDataHb = (params?: AsdTmLl | any) =>
  defHttp.post<AsdTmLl>({ url: adminPath + '/asd/ll/asdTmLl/hbLlData', params });

export const barCodeCheck = (params?: AsdTmLl | any) =>
  defHttp.post<AsdTmLl>({ url: adminPath + '/asd/ll/asdTmLl/analyLl', params });

export const encLl = (params?: AsdTmLl | any) =>
  defHttp.post<AsdTmLl>({ url: adminPath + '/asd/ll/asdTmLl/encLl', params });
