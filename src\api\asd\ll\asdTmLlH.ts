/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface AsdTmLlH extends BasicModel<AsdTmLlH> {
  djno?: string; // 单据号
  cwhcode?: string; // 申请仓库
  isNewRecord?: boolean;
  asdTmLlRdsList?: any[]; // 子表列表
}

export interface AsdTmLlRds extends BasicModel<AsdTmLlRds> {
  status?: string;
}
export interface AsdTmLlHRds extends BasicModel<AsdTmLlHRds> {
  id?: string; // 申请仓库
  cinvcode?: string; // 单据号
}

export const listData = (params?: any) =>
  defHttp.post<AsdTmLlH>({ url: adminPath + '/asd/ll/asdTmLlH/masterListData', params });

export const asdTmLlHList = (params?: AsdTmLlH | any) =>
  defHttp.get<AsdTmLlH>({ url: adminPath + '/asd/ll/asdTmLlH/list', params });

export const asdTmLlHListData = (params?: AsdTmLlH | any) =>
  defHttp.post<Page<AsdTmLlH>>({ url: adminPath + '/asd/ll/asdTmLlH/listData', params });

export const asdTmLlRdsListData = (params?: AsdTmLlRds | any) =>
  defHttp.post<Page<AsdTmLlRds>>({
    url: adminPath + '/asd/ll/asdTmLlH/asdTmLlRdsListData',
    params,
  });

export const asdTmLlHForm = (params?: AsdTmLlH | any) =>
  defHttp.get<AsdTmLlH>({ url: adminPath + '/asd/ll/asdTmLlH/form', params });

export const asdTmLlHPosForm = (params?: AsdTmLlHRds | any) =>
  defHttp.get<AsdTmLlHRds>({ url: adminPath + '/asd/ll/asdTmLlH/posForm', params });

export const asdTmLlHSave = (params?: any, data?: AsdTmLlH | any) =>
  defHttp.postJson<AsdTmLlH>({ url: adminPath + '/asd/ll/asdTmLlH/save', params, data });

export const asdTmLlHSaveLl = (params?: any, data?: AsdTmLlH | any) =>
  defHttp.postJson<AsdTmLlH>({ url: adminPath + '/asd/ll/asdTmLlH/saveLl', params, data });

export const asdTmLlHDelete = (params?: AsdTmLlH | any) =>
  defHttp.get<AsdTmLlH>({ url: adminPath + '/asd/ll/asdTmLlH/delete', params });

export const fileDataUpload = (params?: AsdTmLlRds | any) =>
  defHttp.get<AsdTmLlRds>({
    url: adminPath + '/asd/ll/asdTmLlH/fileDataUpload',
    params,
  });
