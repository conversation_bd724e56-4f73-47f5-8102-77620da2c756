/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface AsdTmLlPos extends BasicModel<AsdTmLlPos> {
  did?: string; // did
  cposcode?: string; // 货位编码
  cbatch?: string; // 批次
  iqty?: number; // 数量
  bcheck?: string; // 是否下架
}

export const asdTmLlPosList = (params?: AsdTmLlPos | any) =>
  defHttp.get<AsdTmLlPos>({ url: adminPath + '/asd/ll/asdTmLlPos/list', params });

export const asdTmLlPosListData = (params?: AsdTmLlPos | any) =>
  defHttp.post<Page<AsdTmLlPos>>({ url: adminPath + '/asd/ll/asdTmLlPos/listData', params });

export const asdTmLlPosForm = (params?: AsdTmLlPos | any) =>
  defHttp.get<AsdTmLlPos>({ url: adminPath + '/asd/ll/asdTmLlPos/form', params });

export const asdTmLlPosSave = (params?: any, data?: AsdTmLlPos | any) =>
  defHttp.postJson<AsdTmLlPos>({ url: adminPath + '/asd/ll/asdTmLlPos/save', params, data });

export const asdTmLlPosDelete = (params?: AsdTmLlPos | any) =>
  defHttp.get<AsdTmLlPos>({ url: adminPath + '/asd/ll/asdTmLlPos/delete', params });

export const bacthSave = (params?: any, data?: AsdTmLlPos | any) =>
  defHttp.postJson<AsdTmLlPos>({ url: adminPath + '/asd/ll/asdTmLlPos/bacthSave', params, data });
