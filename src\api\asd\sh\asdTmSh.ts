/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface AsdTmSh extends BasicModel<AsdTmSh> {
  cinvcode?: string; // 存货编码
  cbatch?: string; // 批次
  cwhcode?: string; // 仓库
  iqty?: number; // 数量
  sumQty?: number; // 累计上架数
  cpersoncode?: string; // 操作者
  cvencode?: string; // 供应商
}

export const asdTmShList = (params?: AsdTmSh | any) =>
  defHttp.get<AsdTmSh>({ url: adminPath + '/asd/sh/asdTmSh/list', params });

export const asdTmShListData = (params?: AsdTmSh | any) =>
  defHttp.post<Page<AsdTmSh>>({ url: adminPath + '/asd/sh/asdTmSh/listData', params });

export const asdTmShForm = (params?: AsdTmSh | any) =>
  defHttp.get<AsdTmSh>({ url: adminPath + '/asd/sh/asdTmSh/form', params });

export const asdTmShSave = (params?: any, data?: AsdTmSh | any) =>
  defHttp.postJson<AsdTmSh>({ url: adminPath + '/asd/sh/asdTmSh/save', params, data });

export const asdTmShImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/asd/sh/asdTmSh/importData',
      onUploadProgress,
    },
    params,
  );

export const asdTmShDelete = (params?: AsdTmSh | any) =>
  defHttp.get<AsdTmSh>({ url: adminPath + '/asd/sh/asdTmSh/delete', params });
