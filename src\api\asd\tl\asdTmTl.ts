/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface AsdTmTl extends BasicModel<AsdTmTl> {
  djno?: string; // 退料单号
  cinvcode?: string; // 物料编码
  iqty?: number; // 数量
  cwhcode?: string; // 仓库
  cbatch?: string; // 批次
  tlQty?: number; // 累计退料数
  tlBy?: string; // 退料人
  retrace?: string; // 追溯号
}

export const asdTmTlList = (params?: AsdTmTl | any) =>
  defHttp.get<AsdTmTl>({ url: adminPath + '/asd/tl/asdTmTl/list', params });

export const asdTmTlListData = (params?: AsdTmTl | any) =>
  defHttp.post<Page<AsdTmTl>>({ url: adminPath + '/asd/tl/asdTmTl/listData', params });

export const asdTmTlForm = (params?: AsdTmTl | any) =>
  defHttp.get<AsdTmTl>({ url: adminPath + '/asd/tl/asdTmTl/form', params });

export const asdTmTlSave = (params?: any, data?: AsdTmTl | any) =>
  defHttp.postJson<AsdTmTl>({ url: adminPath + '/asd/tl/asdTmTl/save', params, data });

export const asdTmTlDelete = (params?: AsdTmTl | any) =>
  defHttp.get<AsdTmTl>({ url: adminPath + '/asd/tl/asdTmTl/delete', params });

export const batchSave = (params?: any, data?: AsdTmTl | any) =>
  defHttp.postJson<AsdTmTl>({ url: adminPath + '/asd/tl/asdTmTl/batchSave', params, data });

export const fileDataUpload = (params?: AsdTmTl | any) =>
  defHttp.get<AsdTmTl>({
    url: adminPath + '/asd/tl/asdTmTl/fileDataUpload',
    params,
  });
