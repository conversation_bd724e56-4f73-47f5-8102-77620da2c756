/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface AsdTmTz extends BasicModel<AsdTmTz> {
  djno?: string; // 调整单号
  cinvcode?: string; // 物料编码
  iqty?: number; // 数量
  cwhcode?: string; // 仓库
  cbatch?: string; // 批次
  cbcpos?: string; // 调整前货位
  cacpos?: string; // 调整后货位
}

export const asdTmTzList = (params?: AsdTmTz | any) =>
  defHttp.get<AsdTmTz>({ url: adminPath + '/asd/tz/asdTmTz/list', params });

export const asdTmTzListData = (params?: AsdTmTz | any) =>
  defHttp.post<Page<AsdTmTz>>({ url: adminPath + '/asd/tz/asdTmTz/listData', params });

export const asdTmTzForm = (params?: AsdTmTz | any) =>
  defHttp.get<AsdTmTz>({ url: adminPath + '/asd/tz/asdTmTz/form', params });

export const asdTmTzSave = (params?: any, data?: AsdTmTz | any) =>
  defHttp.postJson<AsdTmTz>({ url: adminPath + '/asd/tz/asdTmTz/save', params, data });

export const asdTmTzDelete = (params?: AsdTmTz | any) =>
  defHttp.get<AsdTmTz>({ url: adminPath + '/asd/tz/asdTmTz/delete', params });

export const asdTmTzSp = (params?: AsdTmTz | any) =>
  defHttp.get<AsdTmTz>({
    url: adminPath + '/asd/tz/asdTmTz/batchSp',
    params,
  });
