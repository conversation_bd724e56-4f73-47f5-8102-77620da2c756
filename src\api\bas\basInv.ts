/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface BasInv extends BasicModel<BasInv> {
  cinvcode?: string; // 存货编码
  cinvaddcode?: string; // 存货代码
  cinvname?: string; // 存货名称
  cinvstd?: string; // 批次
}

export const basInvListData = (params?: BasInv | any) =>
  defHttp.post<Page<BasInv>>({ url: adminPath + '/bas/basInventory/listData', params });
