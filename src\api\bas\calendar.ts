/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface TestData extends BasicModel<TestData> {
  testInput?: string; // 单行文本
  testTextarea?: string; // 多行文本
  testSelect?: string; // 下拉框
  testSelectMultiple?: string; // 下拉多选
  testRadio?: string; // 单选框
  testCheckbox?: string; // 复选框
  testDate?: string; // 日期选择
  testDatetime?: string; // 日期时间
  testUser?: any; // 用户选择
  testOffice?: any; // 机构选择
  testAreaCode?: string; // 区域选择
  testAreaName?: string; // 区域名称
  testDataChildList?: any[]; // 子表列表
}

// export const teamList = (params?: TestData | any) =>
//   defHttp.get<TestData>({ url: adminPath + '/bas/team/list', params });

// export const teamListData = (params?: TestData | any) =>
//   defHttp.post<Page<TestData>>({ url: adminPath + '/bas/team/listData', params });

// export const teamForm = (params?: TestData | any) =>
//   defHttp.get<TestData>({ url: adminPath + '/bas/team/form', params });

// export const teamSave = (params?: any, data?: TestData | any) =>
//   defHttp.postJson<TestData>({ url: adminPath + '/bas/team/save', params, data });

// export const teamDisable = (params?: TestData | any) =>
//   defHttp.get<TestData>({ url: adminPath + '/bas/team/disable', params });

// export const teamEnable = (params?: TestData | any) =>
//   defHttp.get<TestData>({ url: adminPath + '/bas/team/enable', params });


// {
//   year: 2023;
//   month: 6;
//   day: 1;
// }
export const findYMD = (params?: any) =>
  defHttp.post<any>({ url: adminPath + '/bas/calendar/findYMD', params });

// {
//   teamNames: 晚班,早班,中班,
//   teamLists:[],
//   cdate: 2023-05-25,
// }
export const saveStr = (params?: any) =>
  defHttp.post<any>({ url: adminPath + '/bas/calendar/saveStr', params });


  export const initCalendar = (params?: any) =>
  defHttp.post<any>({ url: adminPath + '/bas/calendar/initCalendar', params });
  