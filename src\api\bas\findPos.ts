/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { TreeDataModel, TreeModel } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface Position extends TreeModel<Position> {
  cposcode?: string; // 货位编码
  cposname?: string; // 货位名称
  cwhcode?: string; // 仓库
}

export const findPosTop = (params?: any) =>
  defHttp.get<TreeDataModel[]>({ url: adminPath + '/bas/api/v1/findPosTop', params });

export const findPosRowByOne = (params?: any) =>
  defHttp.get<TreeDataModel[]>({ url: adminPath + '/bas/api/v1/findPosRowByOne', params });

export const findPosEndByOne = (params?: any) =>
  defHttp.get<TreeDataModel[]>({ url: adminPath + '/bas/api/v1/findPosEndByOne', params });
