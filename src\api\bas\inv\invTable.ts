/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface InvTable extends BasicModel<InvTable> {
  cinvcode?: string; // 存货编码
  cinvaddcode?: string; // 存货代码
  cinvname?: string; // 存货名称
  cinvstd?: string; // 规格型号
  cinvcname?: string; // 存货大类
  ilowsum?: number; // 最低库存
  itopsum?: number; // 最高库存
  isafenum?: number; // 在库量
  lowx?: number; // 最小值差
  topx?: number; // 最大值差
}

export const invTableList = (params?: InvTable | any) =>
  defHttp.get<InvTable>({ url: adminPath + '/bas/inv/invTable/list', params });

export const invTableListData = (params?: InvTable | any) =>
  defHttp.post<Page<InvTable>>({ url: adminPath + '/bas/inv/invTable/listData2', params });

export const invTableForm = (params?: InvTable | any) =>
  defHttp.get<InvTable>({ url: adminPath + '/bas/inv/invTable/form', params });

export const invTableSave = (params?: any, data?: InvTable | any) =>
  defHttp.postJson<InvTable>({ url: adminPath + '/bas/inv/invTable/save', params, data });

export const invTableImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/bas/inv/invTable/importData',
      onUploadProgress,
    },
    params,
  );

export const invTableDelete = (params?: InvTable | any) =>
  defHttp.get<InvTable>({ url: adminPath + '/bas/inv/invTable/delete', params });
