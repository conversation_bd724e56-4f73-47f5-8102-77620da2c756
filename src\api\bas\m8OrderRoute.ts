import { route } from 'koa-route';
/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface M8OrderRoute extends BasicModel<M8OrderRoute> {
  routeId?: string; // 工艺ID
  orderId?: string; // 订单ID
  picId?: string; // 图纸ID
  sortNum?: number; // 顺序号
  operCode?: string; // 工序编码
  jgtime?: number; // 加工时长
  tstime?: number; // 调试时长
  fprice?: number; // 单价
  teamQty?: number; // 班次数
  routeList: any;
}

export interface M8SxOrderRoute extends BasicModel<M8SxOrderRoute> {
  'oper.operCode'?: object;
  'oper.operName'?: object;
  jgtime?: string;
  tstime?: string;
  csort?: number;
  fprice?: number;
  teamQty?: number;
  iqty?: number;
  beginTime?: string;
  endTime?: string;
  oldOper?: any;
  oldSort?: number;
  oldQty?: number;
  routeList: any;
}

export const m8OrderRouteList = (params?: M8OrderRoute | any) =>
  defHttp.get<M8OrderRoute>({ url: adminPath + '/m8/orderRoute/list', params });

export const m8OrderRouteListData = (params?: M8OrderRoute | any) =>
  defHttp.post<Page<M8OrderRoute>>({ url: adminPath + '/m8/orderRoute/listData', params });

export const m8OrderRouteForm = (params?: M8OrderRoute | any) =>
  defHttp.get<M8OrderRoute>({ url: adminPath + '/m8/order/pic/routeForm', params });

export const m8OrderRouteSave = (params?: any, data?: M8OrderRoute | any) =>
  defHttp.postJson<M8OrderRoute>({ url: adminPath + '/m8/order/pic/routeSave', params, data });

export const m8OrderRouteDelete = (params?: M8OrderRoute | any) =>
  defHttp.get<M8OrderRoute>({ url: adminPath + '/m8/orderRoute/delete', params });

export const sxRouteForm = (params?: M8OrderRoute | any) =>
  defHttp.get<M8OrderRoute>({ url: adminPath + '/m8/order/pic/batchSxForm', params });

export const M8SxRouteSave = (params?: any, data?: M8SxOrderRoute | any) =>
  defHttp.postJson<M8SxOrderRoute>({ url: adminPath + '/m8/order/pic/sxBatchSave', params, data });
 

export const orderSxDelete = (params?: M8OrderRoute | any) =>
  defHttp.get<M8OrderRoute>({ url: adminPath + '/m8/order/sx/delete', params });