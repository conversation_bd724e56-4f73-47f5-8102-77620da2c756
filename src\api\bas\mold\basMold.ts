/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page, TreeDataModel } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface BasMold extends BasicModel<BasMold> {
  id?: string;
  code?: string; // 模具编码
  lastNum?: string;
  name?: string; // 模具名称
  caddcode?: string; // 模具代码
  moldStatus?: string; // 模具状态
  locationCode?: string; // 所在位置编码
  locationName?: string; // 所在位置名称
  depCode?: string; // 所在部门编码
  depName?: string; // 所在部门名称
  bcomomMode?: string; // 是否共模
  venName?: string; // 供应商名称
  buyDate?: string; // 购买日期
  makeDate?: string; // 制作日期
  installDate?: string; // 安装日期
  checkDate?: string; // 验收日期
  useDate?: string; // 使用日期
  expiryDate?: string; // 保修截止日期
  iquantity?: number; // 理论寿命
  iqty?: number; // 剩余寿命
  useQty?: number; // 加工次数
  addQty?: number; // 追加寿命
  byQty?: number; // 保养次数
  checker?: string; // 点检人
  operCode?: string; // 工序
  clsCode?: string; // 模具类型
  createByName?: string; // 创建人名称
}

export const basMoldList = (params?: BasMold | any) =>
  defHttp.get<BasMold>({ url: adminPath + '/bas/mold/basMold/list', params });

export const basMoldListData = (params?: BasMold | any) =>
  defHttp.post<Page<BasMold>>({ url: adminPath + '/bas/mold/basMold/listData', params });

export const basMoldForm = (params?: BasMold | any) =>
  defHttp.get<BasMold>({ url: adminPath + '/bas/mold/basMold/form', params });

export const basMoldSave = (params?: any, data?: BasMold | any) =>
  defHttp.postJson<BasMold>({ url: adminPath + '/bas/mold/basMold/save', params, data });

export const basMoldImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/bas/mold/basMold/importData',
      onUploadProgress,
    },
    params,
  );

export const basMoldDelete = (params?: BasMold | any) =>
  defHttp.get<BasMold>({ url: adminPath + '/bas/mold/basMold/delete', params });

export const basMoldTreeData = (params?: any) =>
  defHttp.get<TreeDataModel[]>({ url: adminPath + '/bas/mold/basMoldCls/treeData', params });

export const basMoldAddUseQty = (params?: BasMold | any) =>
  defHttp.get<BasMold>({ url: adminPath + '/bas/mold/basMold/addUseQty', params });

export const findKeepListData = (params?: BasMold | any) =>
  defHttp.get<BasMold>({ url: adminPath + '/bas/mold/basMold/findKeepListData', params });
