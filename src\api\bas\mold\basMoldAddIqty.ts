/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface BasMoldAddIqty extends BasicModel<BasMoldAddIqty> {
  moldCode?: string; // 模具编码
  addQty?: number; // 追加次数
  createByName?: string; // 创建者名称
}

export const basMoldAddIqtyList = (params?: BasMoldAddIqty | any) =>
  defHttp.get<BasMoldAddIqty>({ url: adminPath + '/bas/mold/basMoldAddIqty/list', params });

export const basMoldAddIqtyListData = (params?: BasMoldAddIqty | any) =>
  defHttp.post<Page<BasMoldAddIqty>>({ url: adminPath + '/bas/mold/basMoldAddIqty/listData', params });

export const basMoldAddIqtyForm = (params?: BasMoldAddIqty | any) =>
  defHttp.get<BasMoldAddIqty>({ url: adminPath + '/bas/mold/basMoldAddIqty/form', params });

export const basMoldAddIqtySave = (params?: any, data?: BasMoldAddIqty | any) =>
  defHttp.postJson<BasMoldAddIqty>({ url: adminPath + '/bas/mold/basMoldAddIqty/save', params, data });

export const basMoldAddIqtyDelete = (params?: BasMoldAddIqty | any) =>
  defHttp.get<BasMoldAddIqty>({ url: adminPath + '/bas/mold/basMoldAddIqty/delete', params });
