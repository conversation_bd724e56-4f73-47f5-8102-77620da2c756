/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { TreeDataModel, TreeModel } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface BasMoldCls extends TreeModel<BasMoldCls> {
  code?: string; // 工装类型编码
  name?: string; // 工装类型名称
}

export const basMoldClsList = (params?: BasMoldCls | any) =>
  defHttp.get<BasMoldCls>({ url: adminPath + '/bas/mold/basMoldCls/list', params });

export const basMoldClsListData = (params?: BasMoldCls | any) =>
  defHttp.post<BasMoldCls[]>({ url: adminPath + '/bas/mold/basMoldCls/listData', params });

export const basMoldClsForm = (params?: BasMoldCls | any) =>
  defHttp.get<BasMoldCls>({ url: adminPath + '/bas/mold/basMoldCls/form', params });

export const basMoldClsCreateNextNode = (params?: BasMoldCls | any) =>
  defHttp.get<BasMoldCls>({ url: adminPath + '/bas/mold/basMoldCls/createNextNode', params });

export const basMoldClsSave = (params?: any, data?: BasMoldCls | any) =>
  defHttp.postJson<BasMoldCls>({ url: adminPath + '/bas/mold/basMoldCls/save', params, data });

export const basMoldClsDisable = (params?: BasMoldCls | any) =>
  defHttp.get<BasMoldCls>({ url: adminPath + '/bas/mold/basMoldCls/disable', params });

export const basMoldClsEnable = (params?: BasMoldCls | any) =>
  defHttp.get<BasMoldCls>({ url: adminPath + '/bas/mold/basMoldCls/enable', params });

export const basMoldClsDelete = (params?: BasMoldCls | any) =>
  defHttp.get<BasMoldCls>({ url: adminPath + '/bas/mold/basMoldCls/delete', params });

export const basMoldClsTreeData = (params?: any) =>
  defHttp.get<TreeDataModel[]>({ url: adminPath + '/bas/mold/basMoldCls/treeData', params });
