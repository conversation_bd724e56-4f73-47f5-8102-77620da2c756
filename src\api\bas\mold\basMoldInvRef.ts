/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface BasMoldInvRef extends BasicModel<BasMoldInvRef> {
  moldCode?: string; // 模具编码
  lastNum?: string; // 模具尾号
  invCode?: string; // 存货编码
  operCode?: string; // 工序
}

export const basMoldInvRefList = (params?: BasMoldInvRef | any) =>
  defHttp.get<BasMoldInvRef>({ url: adminPath + '/bas/mold/basMoldInvRef/list', params });

export const basMoldInvRefListData = (params?: BasMoldInvRef | any) =>
  defHttp.post<Page<BasMoldInvRef>>({
    url: adminPath + '/bas/mold/basMoldInvRef/listData',
    params,
  });

export const basMoldInvRefForm = (params?: BasMoldInvRef | any) =>
  defHttp.get<BasMoldInvRef>({ url: adminPath + '/bas/mold/basMoldInvRef/form', params });

export const basMoldInvRefSave = (params?: any, data?: BasMoldInvRef | any) =>
  defHttp.postJson<BasMoldInvRef>({
    url: adminPath + '/bas/mold/basMoldInvRef/save',
    params,
    data,
  });

export const basMoldInvRefImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/bas/mold/basMoldInvRef/importData',
      onUploadProgress,
    },
    params,
  );

export const basMoldInvRefDelete = (params?: BasMoldInvRef | any) =>
  defHttp.get<BasMoldInvRef>({ url: adminPath + '/bas/mold/basMoldInvRef/delete', params });

export const getBasMoldInvRef = (params?: BasMoldInvRef | any) =>
  defHttp.get<BasMoldInvRef>({
    url: adminPath + '/bas/mold/basMoldInvRef/findInvByCodeAndLastNum',
    params,
  });
