import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel } from '../../model/baseModel';

const { adminPath } = useGlobSetting();

export interface Route extends BasicModel<Route> {
  routeCode?: string; // 单行文本
  routeName?: string; // 单行文本
  remarks?: string; // 单行文本
  operList?: any[]; // 子表列表
}

export const routeListData = (params?: any) =>
  defHttp.get<any>({ url: adminPath + '/bas/route/listData', params }); //这个路径是后台的路径，主表数据

export const routeDelete = (params?: any) =>
  defHttp.get<any>({ url: adminPath + '/bas/route/delete', params }); //主表的删除接口

export const routeChildListData = (params?: any) =>
  defHttp.get<any>({ url: adminPath + '/bas/route/subListData', params }); //子表的数据接口

export const routeSaveListData = (params?: any, data?: Route | any) =>
  defHttp.postJson<any>({ url: adminPath + '/bas/route/save', params, data }); //主表的编辑数据接口

export const routeForm = (params?: Route | any) =>
  defHttp.get<Route>({ url: adminPath + '/bas/route/form', params }); //表单中主表的数据接口

export const operTreeData = (params?: Route | any) =>
  defHttp.get<Route>({
    url: adminPath + '/bas/oper/treeData?isShowCode=true',
    params,
  }); //表单中主表的数据接口
