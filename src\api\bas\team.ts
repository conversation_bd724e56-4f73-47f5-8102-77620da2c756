/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface Team extends BasicModel<Team> {
  name?: string; // 班组名称
  status?: string; // 状态
  code?: string; // 班组编号
  toworkdate?: string; // 上班时间
  offworkdate?: string; // 下班时间
  strestdate?: string; // 开始休息时间
  edrestdate?: string; // 结束休息时间
  workhours?: number; // 工作时长
  remarks?: string; // 备注
}

export const teamList = (params?: Team | any) =>
  defHttp.get<Team>({ url: adminPath + '/bas/team/list', params });

export const teamListData = (params?: Team | any) =>
  defHttp.post<Page<Team>>({ url: adminPath + '/bas/team/listData', params });

export const teamForm = (params?: Team | any) =>
  defHttp.get<Team>({ url: adminPath + '/bas/team/form', params });

export const teamSave = (params?: any, data?: Team | any) =>
  defHttp.postJson<Team>({ url: adminPath + '/bas/team/save', params, data });

export const teamDisable = (params?: Team | any) =>
  defHttp.get<Team>({ url: adminPath + '/bas/team/disable', params });

export const teamEnable = (params?: Team | any) =>
  defHttp.get<Team>({ url: adminPath + '/bas/team/enable', params });

export const teamDelete = (params?: Team | any) =>
  defHttp.get<Team>({ url: adminPath + '/bas/team/delete', params });

// // 自定义列表接口
// export const custom = (params?: any) =>
//   defHttp.get({ url: adminPath + '/layout/layVoucherViewListQueryCol/listData2', params });
// // 自定义表单接口
// export const customfrom = (params?: any) =>
//   defHttp.get({ url: adminPath + '/layout/layVoucherFormListCol/listData2', params });
// // 自定义表格接口
// export const customtable = (params?: any) =>
//   defHttp.get({ url: adminPath + '/layout/layVoucherTableCol/listData2', params });
// // 自定义表格上方按钮接口
// export const custombtnTop = (params?: any) =>
//   defHttp.get({ url: adminPath + '/layout/layVoucherTabToolbar/btnData', params });
// // 自定义表格右边按钮接口
// export const custombtnRight = (params?: any) =>
//   defHttp.get({ url: adminPath + '/layout/layVoucherTabRightOpe/btnData', params });

// // 万能下拉树形组件接口
// export const treeSelect = (params?: any) => defHttp.get({ url: adminPath + params.url, params });
