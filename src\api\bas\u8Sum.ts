/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface u8Sum extends BasicModel<u8Sum> {
  cwhcode?: string; // 仓库
  cposcode?: string; // 货位编码
  cposname?: string; // 货位名称
  cinvcode?: string; // 存货
  cbatch?: string; // 批次
  iquantity?: string;
  iqty?: string;
}

export const u8SumListData = (params?: u8Sum | any) =>
  defHttp.post<Page<u8Sum>>({ url: adminPath + '/wh/u8/u8InvpositionSum/listData', params });
