/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface ExtdBillContrast extends BasicModel<ExtdBillContrast> {
  billVAId?: string; // 附表单据标识
  tableName?: string; // 表类型标识
  colKey?: string; // 系统KEY
  colName?: string; // 系统名称
  extdColKey?: string; // EXTD系统KEY
  extdColName?: string; // EXTD系统名称
}

export const extdBillContrastList = (params?: ExtdBillContrast | any) =>
  defHttp.get<ExtdBillContrast>({
    url: adminPath + '/extd/bill/view/cont/extdBillContrast/list',
    params,
  });

export const extdBillContrastListData = (params?: ExtdBillContrast | any) =>
  defHttp.post<Page<ExtdBillContrast>>({
    url: adminPath + '/extd/bill/view/cont/extdBillContrast/listData',
    params,
  });

export const extdBillContrastForm = (params?: ExtdBillContrast | any) =>
  defHttp.get<ExtdBillContrast>({
    url: adminPath + '/extd/bill/view/cont/extdBillContrast/form',
    params,
  });

export const extdBillContrastSave = (params?: any, data?: ExtdBillContrast | any) =>
  defHttp.postJson<ExtdBillContrast>({
    url: adminPath + '/extd/bill/view/cont/extdBillContrast/save',
    params,
    data,
  });

export const extdBillContrastImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/extd/bill/view/cont/extdBillContrast/importData',
      onUploadProgress,
    },
    params,
  );

export const extdBillContrastDelete = (params?: ExtdBillContrast | any) =>
  defHttp.get<ExtdBillContrast>({
    url: adminPath + '/extd/bill/view/cont/extdBillContrast/delete',
    params,
  });
