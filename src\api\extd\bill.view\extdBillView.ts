/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface ExtdBillView extends BasicModel<ExtdBillView> {
  billId?: string; // 单据标识
  dataBase?: string; // 数据源
  mainClassName?: string; // 主类全名
  viewName?: string; // 视图名
  reColName?: string; // 回写字段
  reColVal?: string; // 回写值
  viewSql?: string; // 视图代码
  level?: string; //层级
}

export const extdBillViewList = (params?: ExtdBillView | any) =>
  defHttp.get<ExtdBillView>({ url: adminPath + '/extd/bill/view/extdBillView/list', params });

export const extdBillViewListData = (params?: ExtdBillView | any) =>
  defHttp.post<Page<ExtdBillView>>({
    url: adminPath + '/extd/bill/view/extdBillView/listData',
    params,
  });

export const extdBillViewForm = (params?: ExtdBillView | any) =>
  defHttp.get<ExtdBillView>({ url: adminPath + '/extd/bill/view/extdBillView/form', params });

export const extdBillViewSave = (params?: any, data?: ExtdBillView | any) =>
  defHttp.postJson<ExtdBillView>({
    url: adminPath + '/extd/bill/view/extdBillView/save',
    params,
    data,
  });

export const extdBillViewDelete = (params?: ExtdBillView | any) =>
  defHttp.get<ExtdBillView>({ url: adminPath + '/extd/bill/view/extdBillView/delete', params });

export const extdBillViewExecSql = (params?: ExtdBillView | any) =>
  defHttp.get<ExtdBillView>({
    url: adminPath + '/extd/bill/view/extdBillView/execSqlCreateView',
    params,
  });
