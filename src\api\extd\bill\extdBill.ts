/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page, TreeDataModel } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface ExtdBill extends BasicModel<ExtdBill> {
  code?: string; // 单据标识
  name?: string; // 单据名称
  extdCode?: string; // EXTD系统
  extdType?: string; // 同步方式
  ids?: string; //单据id集
  codes?: string; //单据id集
  mainClassName?: string; // 单据类全名
}

export const extdBillList = (params?: ExtdBill | any) =>
  defHttp.get<ExtdBill>({ url: adminPath + '/extd/bill/extdBill/list', params });

export const extdBillListData = (params?: ExtdBill | any) =>
  defHttp.post<Page<ExtdBill>>({ url: adminPath + '/extd/bill/extdBill/listData', params });

export const extdBillForm = (params?: ExtdBill | any) =>
  defHttp.get<ExtdBill>({ url: adminPath + '/extd/bill/extdBill/form', params });

export const extdBillSave = (params?: any, data?: ExtdBill | any) =>
  defHttp.postJson<ExtdBill>({ url: adminPath + '/extd/bill/extdBill/save', params, data });

export const extdBillImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/extd/bill/extdBill/importData',
      onUploadProgress,
    },
    params,
  );

export const extdBillDisable = (params?: ExtdBill | any) =>
  defHttp.get<ExtdBill>({ url: adminPath + '/extd/bill/extdBill/disable', params });

export const extdBillEnable = (params?: ExtdBill | any) =>
  defHttp.get<ExtdBill>({ url: adminPath + '/extd/bill/extdBill/enable', params });

export const extdBillDelete = (params?: ExtdBill | any) =>
  defHttp.get<ExtdBill>({ url: adminPath + '/extd/bill/extdBill/delete', params });

export const extdBillPull = (params?: ExtdBill | any) =>
  defHttp.get<ExtdBill>({ url: adminPath + '/extd/bill/extdBill/pullBillData', params });

export const extdBillRef = (params?: ExtdBill | any) =>
  defHttp.get<ExtdBill>({ url: adminPath + '/extd/bill/extdBill/refBillData', params });

export const extdBillTreeData = (params?: any) =>
  defHttp.get<TreeDataModel[]>({ url: adminPath + '/extd/extdSystem/treeData', params });
