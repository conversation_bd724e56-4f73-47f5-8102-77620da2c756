/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { TreeDataModel, TreeModel } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface ExtdSystem extends TreeModel<ExtdSystem> {
  code?: string; // EXTD系统标志
  name?: string; // EXTD系统名称
  version?: string; // EXTD系统版本
}

export const extdSystemList = (params?: ExtdSystem | any) =>
  defHttp.get<ExtdSystem>({ url: adminPath + '/extd/extdSystem/list', params });

export const extdSystemListData = (params?: ExtdSystem | any) =>
  defHttp.post<ExtdSystem[]>({ url: adminPath + '/extd/extdSystem/listData', params });

export const extdSystemForm = (params?: ExtdSystem | any) =>
  defHttp.get<ExtdSystem>({ url: adminPath + '/extd/extdSystem/form', params });

export const extdSystemCreateNextNode = (params?: ExtdSystem | any) =>
  defHttp.get<ExtdSystem>({ url: adminPath + '/extd/extdSystem/createNextNode', params });

export const extdSystemSave = (params?: any, data?: ExtdSystem | any) =>
  defHttp.postJson<ExtdSystem>({ url: adminPath + '/extd/extdSystem/save', params, data });

export const extdSystemImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/extd/extdSystem/importData',
      onUploadProgress,
    },
    params,
  );

export const extdSystemDisable = (params?: ExtdSystem | any) =>
  defHttp.get<ExtdSystem>({ url: adminPath + '/extd/extdSystem/disable', params });

export const extdSystemEnable = (params?: ExtdSystem | any) =>
  defHttp.get<ExtdSystem>({ url: adminPath + '/extd/extdSystem/enable', params });

export const extdSystemDelete = (params?: ExtdSystem | any) =>
  defHttp.get<ExtdSystem>({ url: adminPath + '/extd/extdSystem/delete', params });

export const extdSystemTreeData = (params?: any) =>
  defHttp.get<TreeDataModel[]>({ url: adminPath + '/extd/extdSystem/treeData', params });
