/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface M8MorderDetail extends BasicModel<M8MorderDetail> {
  moid?: number; // moid
  modid?: number; // modid
  mocode?: string; // 订单号
  sortseq?: number; // 行号
  invcode?: string; // 物料编码
  cinvname?: string; // 物料名称
  cinvstd?: string; // 物料规格
  qty?: number; // 生产数量
  startdate?: string; // 开工日期
  duedate?: string; // 完工日期
  ccomunitname?: string; // 计量单位
  mdeptcode?: string; // 生产部门
  cdepname?: string; // 部门名称
  remark?: string; // 备注
  lplancode?: string; // 计划批号
  cuserName?: string; // 制单人
}

export const m8MorderDetailList = (params?: M8MorderDetail | any) =>
  defHttp.get<M8MorderDetail>({ url: adminPath + '/hcnj/m8MorderDetail/list', params });

export const m8MorderDetailListData = (params?: M8MorderDetail | any) =>
  defHttp.post<Page<M8MorderDetail>>({ url: adminPath + '/hcnj/m8MorderDetail/listData', params });

export const m8MorderDetailForm = (params?: M8MorderDetail | any) =>
  defHttp.get<M8MorderDetail>({ url: adminPath + '/hcnj/m8MorderDetail/form', params });

export const m8MorderDetailSave = (params?: any, data?: M8MorderDetail | any) =>
  defHttp.postJson<M8MorderDetail>({ url: adminPath + '/hcnj/m8MorderDetail/save', params, data });

export const m8MorderDetailDelete = (params?: M8MorderDetail | any) =>
  defHttp.get<M8MorderDetail>({ url: adminPath + '/hcnj/m8MorderDetail/delete', params });

export const m8MorderDetailClosePrint = (params?: M8MorderDetail | any) =>
  defHttp.get<M8MorderDetail>({ url: adminPath + '/hcnj/m8MorderDetail/closed', params });

export const m8MorderDetailUpdateCount = (params?: M8MorderDetail | any) =>
  defHttp.get<M8MorderDetail>({ url: adminPath + '/hcnj/m8MorderDetail/printCount', params });
