/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../model/baseModel';

const { adminPath } = useGlobSetting();

export type Flow = BasicModel<Flow>;

export interface M8OrderPicFlowOver extends BasicModel<M8OrderPicFlowOver> {
  order?: any;
  orderPic?: any;
  orderFlow?: any;
  oper?: any;
  iqty?: any;
  manCodes?: string; // 负责人编码
  manNames?: string; // 负责人名称
}

export const listData = (params?: any) =>
  defHttp.post<Flow>({ url: adminPath + '/m8/order/flow/listData', params });

export const save = (params?: any) =>
  defHttp.post<Flow>({ url: adminPath + '/m8/order/flow/over/save', params });

export const overForm = (params?: M8OrderPicFlowOver | any) =>
  defHttp.postJson<M8OrderPicFlowOver>({ url: adminPath + '/m8/order/flow/over/form', params });

export const overDelete = (params?: Flow | any) =>
  defHttp.get<Flow>({ url: adminPath + '/m8/order/flow/over/delete', params });

export const listSelectData = (params?: any) =>
  defHttp.post<Flow>({ url: adminPath + '/m8/order/flow/listSelectData', params });
