/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../model/baseModel';

export const { adminPath } = useGlobSetting();

export type Machine = BasicModel<Machine>;

// export const testDataList = (params?: TestData | any) =>
//   defHttp.get<TestData>({ url: adminPath + '/test/testData/list', params });

// export const testDataListData = (params?: TestData | any) =>
//   defHttp.post<Page<TestData>>({ url: adminPath + '/test/testData/listData', params });

export const machineForm = (params?: Machine | any) =>
  defHttp.get<Machine>({ url: adminPath + '/m8/machine/form', params });

export const machineSave = (params?: any, data?: Machine | any) =>
  defHttp.postJson<Machine>({ url: adminPath + '/m8/machine/save', params, data });

export const findSjMachine = (params?: Machine | any) =>
  defHttp.post<Page<Machine>>({ url: adminPath + '/m8/machine/findSjMachine', params });

export const findGcjMachine = (params?: Machine | any) =>
  defHttp.post<Page<Machine>>({ url: adminPath + '/m8/machine/findGcjMachine', params });

// export const testDataEnable = (params?: TestData | any) =>
//   defHttp.get<TestData>({ url: adminPath + '/test/testData/enable', params });

// export const testDataDelete = (params?: TestData | any) =>
//   defHttp.get<TestData>({ url: adminPath + '/test/testData/delete', params });

// export const orderTaskListData = (params?: Machine | any) =>
//   defHttp.post<Page<Machine>>({ url: adminPath + '/m8/machine/task/orderTaskListData', params });

// export const taskLastNodeData = (params?: Machine | any) =>
//   defHttp.post<Page<Machine>>({ url: adminPath + '/m8/machine/task/taskLastNodeData', params });

export const enable = (params?: Machine | any) =>
  defHttp.post<Machine>({ url: adminPath + '/m8/machine/enable', params });

export const disable = (params?: Machine | any) =>
  defHttp.post<Machine>({ url: adminPath + '/m8/machine/disable', params });

export const taskOversave = (params?: any) =>
  defHttp.post<Machine>({ url: adminPath + '/m8/machine/over/save', params });

export const taskDebugsave = (params?: any) =>
  defHttp.post<Machine>({ url: adminPath + '/m8/machine/debug/save', params });

export const checkForm = (params?: Machine | any) =>
  defHttp.get<Machine>({ url: adminPath + '/m8/machine/check/form', params });

export const checksave = (params?: any, data?: any) =>
  defHttp.postJson<any>({ url: adminPath + '/m8/machine/check/save', params, data });

export const refresh = () => defHttp.get<Machine>({ url: adminPath + '/m8/machine/refresh' });
