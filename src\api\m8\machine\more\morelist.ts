import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../../model/baseModel';

const { adminPath } = useGlobSetting();

export interface MachineMore extends BasicModel<MachineMore> {
  taskCode?: string; // 任务编码
  remarks?: string; // 备注
  allowEdit?: boolean; //是否可编辑
  allowDel?: boolean; //是否可删除
  iqty?: string; //数量
  overId?: string; //完工ID
  glManName?: string; //给料人
  llManName?: string; //领料人
}

export const taskMoreListData = (params?: MachineMore | any) =>
  defHttp.post<Page<MachineMore>>({ url: adminPath + '/m8/machine/more/listData', params });

export const save = (params?: any, data?: MachineMore | any) =>
  defHttp.postJson<MachineMore>({ url: adminPath + '/m8/machine/more/save', params, data });

export const listDelete = (params?: MachineMore | any) =>
  defHttp.post<MachineMore>({ url: adminPath + '/m8/machine/more/delete', params });

// export const taskRemarkForm = (params?: MachineMore | any) =>
//   defHttp.post<MachineMore>({ url: adminPath + '/m8/machine/more/form', params });
