import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel } from '/@/api/model/baseModel';

const { adminPath } = useGlobSetting();

export type taskOver = BasicModel<taskOver>;

export const taskOverFormEdit = (params?: taskOver | any) =>
  defHttp.get<taskOver>({ url: adminPath + '/m8/machine/over/form', params });

export const taskOversave = (params?: any) =>
  defHttp.post<taskOver>({ url: adminPath + '/m8/machine/over/save', params });

export const overBackForm = (params?: taskOver | any) =>
  defHttp.get<taskOver>({ url: adminPath + '/m8/machine/over/backForm', params });

export const overBackSave = (params?: any, data?: taskOver | any) =>
  defHttp.postJson<taskOver>({ url: adminPath + '/m8/machine/over/saveBack', params, data });

export const updateCstatus = (params?: taskOver | any) =>
  defHttp.post<taskOver>({ url: adminPath + '/m8/machine/over/updateCstatus', params });
