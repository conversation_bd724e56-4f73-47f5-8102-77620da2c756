/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface M8MachinePro extends BasicModel<M8MachinePro> {
  ctype?: string; // 参数类型
  csource?: string; // 数据来源
  cfield?: string; // 来源属性
  minVal?: string; // 最小值
  maxVal?: string; // 最大值
  refCode?: string; // 对应参数
}

export const m8MachineProList = (params?: M8MachinePro | any) =>
  defHttp.get<M8MachinePro>({ url: adminPath + '/m8/machine/pro/list', params });

export const m8MachineProListData = (params?: M8MachinePro | any) =>
  defHttp.post<Page<M8MachinePro>>({ url: adminPath + '/m8/machine/pro/listData', params });

export const m8MachineProForm = (params?: M8MachinePro | any) =>
  defHttp.get<M8MachinePro>({ url: adminPath + '/m8/machine/pro/form', params });

export const m8MachineProSave = (params?: any, data?: M8MachinePro | any) =>
  defHttp.postJson<M8MachinePro>({ url: adminPath + '/m8/machine/pro/save', params, data });

export const m8MachineProImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/m8/machine/pro/importData',
      onUploadProgress,
    },
    params,
  );

export const m8MachineProDelete = (params?: M8MachinePro | any) =>
  defHttp.get<M8MachinePro>({ url: adminPath + '/m8/machine/pro/delete', params });
