/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../../model/baseModel';

const { adminPath } = useGlobSetting();

export interface M8TaskRemark extends BasicModel<M8TaskRemark> {
  taskCode?: string; // 任务编码
  machineId?: string; // 机床ID
  operCode?: string; // 工序编码
  managerCode?: string; // 负责人编码
  managerName?: string; // 负责人名称
  createBy?: string; // 登记人
  createDate?: string; // 登记时间
  remarks?: string; // 备注
  allowEdit?: boolean; //是否可编辑
  allowDel?: boolean; //是否可删除
}

export const taskRemarkListData = (params?: M8TaskRemark | any) =>
  defHttp.post<Page<M8TaskRemark>>({ url: adminPath + '/m8/machine/remark/listData', params });

export const taskRemarkSave = (params?: any, data?: M8TaskRemark | any) =>
  defHttp.postJson<M8TaskRemark>({ url: adminPath + '/m8/machine/remark/save', params, data });

export const taskRemarkDelete = (params?: M8TaskRemark | any) =>
  defHttp.post<M8TaskRemark>({ url: adminPath + '/m8/machine/remark/delete', params });

export const taskRemarkForm = (params?: M8TaskRemark | any) =>
  defHttp.post<M8TaskRemark>({ url: adminPath + '/m8/machine/remark/form', params });
