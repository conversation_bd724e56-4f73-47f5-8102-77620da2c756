/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../model/baseModel';

const { adminPath } = useGlobSetting();

export type Task = BasicModel<Task>;

// export const testDataList = (params?: TestData | any) =>
//   defHttp.get<TestData>({ url: adminPath + '/test/testData/list', params });

// export const testDataListData = (params?: TestData | any) =>
//   defHttp.post<Page<TestData>>({ url: adminPath + '/test/testData/listData', params });

// export const testDataForm = (params?: TestData | any) =>
//   defHttp.get<TestData>({ url: adminPath + '/test/testData/form', params });

// export const testDataSave = (params?: any, data?: TestData | any) =>
//   defHttp.postJson<TestData>({ url: adminPath + '/test/testData/save', params, data });

// export const testDataDisable = (params?: TestData | any) =>
//   defHttp.get<TestData>({ url: adminPath + '/test/testData/disable', params });

// export const testDataEnable = (params?: TestData | any) =>
//   defHttp.get<TestData>({ url: adminPath + '/test/testData/enable', params });

// export const testDataDelete = (params?: TestData | any) =>
//   defHttp.get<TestData>({ url: adminPath + '/test/testData/delete', params });

export const orderTaskListData = (params?: Task | any) =>
  defHttp.post<Page<Task>>({ url: adminPath + '/m8/machine/task/orderTaskListData', params });

export const taskLastNodeData = (params?: Task | any) =>
  defHttp.post<Page<Task>>({ url: adminPath + '/m8/machine/task/taskLastNodeData', params });

export const updateSjjgTime = (params?: Task | any) =>
  defHttp.post<Task>({ url: adminPath + '/m8/machine/task/updateSjjgTime', params });

export const updateBsj = (params?: Task | any) =>
  defHttp.post<Task>({ url: adminPath + '/m8/machine/task/updateBsj', params });

export const setMachine = (params?: Task | any) =>
  defHttp.post<Task>({ url: adminPath + '/m8/machine/task/setMachine', params });

export const setFirstLevel = (params?: Task | any) =>
  defHttp.post<Task>({ url: adminPath + '/m8/machine/task/setFirstLevel', params });

export const changeStatus = (params?: Task | any) =>
  defHttp.post<Task>({ url: adminPath + '/m8/machine/task/changeStatus', params });

export const saveCfData = (params?: Task | any, data?: any) =>
  defHttp.postJson<Task>({ url: adminPath + '/m8/machine/task/saveCfData', params, data });

export const lxListData = (params?: Task | any) =>
  defHttp.post<Page<Task>>({ url: adminPath + '/m8/machine/task/taskLxjg/listData', params });

export const tasklxList = (params?: Task | any) =>
  defHttp.get<Task>({ url: adminPath + '/m8/machine/task/taskLxjg/list', params });

export const lxjgSelectData = (params?: Task | any) =>
  defHttp.post<Page<Task>>({ url: adminPath + '/m8/machine/task/lxjgSelectData', params });

export const lxjgSave = (params?: Task | any) =>
  defHttp.post<Task>({ url: adminPath + '/m8/machine/task/taskLxjg/save', params });

export const lxjgDelete = (params?: Task | any) =>
  defHttp.post<Task>({ url: adminPath + '/m8/machine/task/taskLxjg/delete', params });

export const taskPoolListData = (params?: Task | any) =>
  defHttp.post<Page<Task>>({
    url: adminPath + '/m8/machine/task/taskPoolListData',
    params,
  });

export const preTaskPoolListData = (params?: Task | any) =>
  defHttp.post<Page<Task>>({
    url: adminPath + '/m8/machine/task/preTaskPoolListData',
    params,
  });
