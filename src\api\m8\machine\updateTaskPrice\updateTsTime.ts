/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../../../model/baseModel';

export const { adminPath } = useGlobSetting();
export type UpdateTask = BasicModel<UpdateTask>;

export const updateTsTime = (params?: UpdateTask | any) =>
  defHttp.get<UpdateTask>({ url: adminPath + '/m8/machine/machine/updateSjtsTime', params });

export const updatePrice = (params?: UpdateTask | any) =>
  defHttp.get<UpdateTask>({ url: adminPath + '/m8/machine/machine/updateFprice', params });
