/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../model/baseModel';

const { adminPath } = useGlobSetting();

export type More = BasicModel<More>;

// export const testDataList = (params?: TestData | any) =>
//   defHttp.get<TestData>({ url: adminPath + '/test/testData/list', params });

// export const testDataListData = (params?: TestData | any) =>
//   defHttp.post<Page<TestData>>({ url: adminPath + '/test/testData/listData', params });

// export const machineForm = (params?: Machine | any) =>
//   defHttp.get<Machine>({ url: adminPath + '/m8/machine/form', params });

// export const machineSave = (params?: any, data?: Machine | any) =>
//   defHttp.postJson<Machine>({ url: adminPath + '/m8/machine/save', params, data });

// export const testDataDisable = (params?: TestData | any) =>
//   defHttp.get<TestData>({ url: adminPath + '/test/testData/disable', params });

// export const testDataEnable = (params?: TestData | any) =>
//   defHttp.get<TestData>({ url: adminPath + '/test/testData/enable', params });

// export const testDataDelete = (params?: TestData | any) =>
//   defHttp.get<TestData>({ url: adminPath + '/test/testData/delete', params });

// export const orderTaskListData = (params?: Machine | any) =>
//   defHttp.post<Page<Machine>>({ url: adminPath + '/m8/machine/task/orderTaskListData', params });

// export const taskLastNodeData = (params?: Machine | any) =>
//   defHttp.post<Page<Machine>>({ url: adminPath + '/m8/machine/task/taskLastNodeData', params });

export const save = (params?: any,data?:any) =>
  defHttp.postJson<More>({ url: adminPath + '/m8/order/flow/more/save', params,data });

export const moreForm = (params?: More | any) =>
  defHttp.get<More>({ url: adminPath + '/m8/order/flow/more/form', params });

export const moreDelete = (params?: More | any) =>
  defHttp.get<More>({ url: adminPath + '/m8/order/flow/more/delete', params });

export const taskMoreSave = (params?: any, data?: any) =>
  defHttp.post<More>({ url: adminPath + '/m8/machine/more/save', params, data });

export const taskMoreForm = (params?: More | any) =>
  defHttp.get<More>({ url: adminPath + '/m8/machine/more/form', params });

export const taskMoreDelete = (params?: More | any) =>
  defHttp.get<More>({ url: adminPath + '/m8/machine/more/delete', params });
