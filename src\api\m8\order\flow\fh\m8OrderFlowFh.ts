/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../../../model/baseModel';

const { adminPath } = useGlobSetting();

export interface M8OrderFlowFh extends BasicModel<M8OrderFlowFh> {
  djNo?: string; // 发货单号
  cusCode?: string; // 客户编码
  sender?: string; // 发货人
  sendDate?: string; // 发货时间
  receiver?: string; // 收货人
  receiveAddress?: string; // 收货时间
  iphone?: string; // 联系电话
  logisticsNo?: string; // 物流单号
  logistics?: string; // 物流公司
  auditor?: string; // 审核人
  auditDate?: string; // 审核时间
  fhStatus?: string; // 状态
  bred?: string; // 是否红字
  m8OrderFlowFhRdsList?: any[]; // 子表列表
  allowEdit?: boolean; //是否可编辑
  allowDel?: boolean; //是否可删除
}

export const m8OrderFlowFhList = (params?: M8OrderFlowFh | any) =>
  defHttp.get<M8OrderFlowFh>({ url: adminPath + '/order/flow/fh/list', params });

export const m8OrderFlowFhListData = (params?: M8OrderFlowFh | any) =>
  defHttp.post<Page<M8OrderFlowFh>>({
    url: adminPath + '/order/flow/fh/listData',
    params,
  });

export const m8OrderFlowFhIdsListData = (params?: any) =>
  defHttp.post<Page<M8OrderFlowFh>>({
    url: adminPath + '/order/flow/fh/m8OrderFlowFhRdsListData',
    params,
  });

export const listSelectData = (params?: any) =>
  defHttp.post<Page<M8OrderFlowFh>>({
    url: adminPath + '/order/flow/fh/listSelectData',
    params,
  });

export const m8OrderFlowFhForm = (params?: M8OrderFlowFh | any) =>
  defHttp.get<M8OrderFlowFh>({ url: adminPath + '/order/flow/fh/form', params });

export const m8OrderFlowFhSave = (params?: any, data?: M8OrderFlowFh | any) =>
  defHttp.postJson<M8OrderFlowFh>({
    url: adminPath + '/order/flow/fh/save',
    params,
    data,
  });

export const m8OrderFlowFhDelete = (params?: M8OrderFlowFh | any) =>
  defHttp.get<M8OrderFlowFh>({ url: adminPath + '/order/flow/fh/delete', params });

export const m8OrderFlowFhSp = (params?: any, data?: M8OrderFlowFh | any) =>
  defHttp.postJson<M8OrderFlowFh>({
    url: adminPath + '/order/flow/fh/batchSp',
    params,
    data,
  });
