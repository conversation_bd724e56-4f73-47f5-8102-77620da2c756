/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page, TreeDataModel } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface M8OperCheck extends BasicModel<M8OperCheck> {
  djNo?: string; // 处理单号
  orderId?: string; // 订单ID
  picId?: string; // 图纸ID
  flowId?: string; // 流程ID
  sxId?: string; // 数铣ID
  machineId: string; // 机床ID
  taskCode?: string; // 任务编号
  checkType?: string; // 检验类型
  operType?: string; // 工序类型
  operCode?: string; // 来源工序
  checkRemarks?: string; // 不合格品描述
  iqty?: number; // 处理数量
  rsQty?: number; // 返工数量
  bfQty?: number; // 报废数量
  checkor?: any; // 检验员
  dealQty?: number; // 不合格品处理数量
  checkResult: string; // 检验结果
  checkStatus: string; // 检验状态
}

export const m8OperCheckList = (params?: M8OperCheck | any) =>
  defHttp.get<M8OperCheck>({ url: adminPath + '/order/m8OperCheck/list', params });

export const m8OperCheckListData = (params?: M8OperCheck | any) =>
  defHttp.post<Page<M8OperCheck>>({ url: adminPath + '/order/m8OperCheck/listData', params });

export const m8OperCheckForm = (params?: M8OperCheck | any) =>
  defHttp.get<M8OperCheck>({ url: adminPath + '/order/m8OperCheck/form', params });

export const findCheckListByPicId = (params?: M8OperCheck | any) =>
  defHttp.post<M8OperCheck>({ url: adminPath + '/order/m8OperCheck/findCheckListByPicId', params });

export const batchSave = (params?: any) =>
  defHttp.post<M8OperCheck>({ url: adminPath + '/order/m8OperCheck/batchSave', params });

export const m8OperCheckSave = (params?: any, data?: M8OperCheck | any) =>
  defHttp.postJson<M8OperCheck>({ url: adminPath + '/order/m8OperCheck/save', params, data });

export const m8OperCheckDelete = (params?: M8OperCheck | any) =>
  defHttp.get<M8OperCheck>({ url: adminPath + '/order/m8OperCheck/delete', params });

export const m8OrderFlowChecktSp = (params?: M8OrderFlowWxOut | any) =>
  defHttp.get<M8OperCheck>({
    url: adminPath + '/order/m8OperCheck/batchSp',
    params,
  });

export const m8OrderFlowChecktDeSp = (params?: M8OrderFlowWxOut | any) =>
  defHttp.get<M8OperCheck>({
    url: adminPath + '/order/m8OperCheck/batchDeSp',
    params,
  });

// 负责人选择
export const manTreeData = (params?: any) =>
  defHttp.get<TreeDataModel[]>({ url: adminPath + '/bas/oper/manTreeData', params });

// 图纸对应检验单选择
export const listSelectData = (params?: any) =>
  defHttp.post<M8OperCheck>({ url: adminPath + '/order/m8OperCheck/listSelectData', params });

// 根据流程ID获取对应的检验单
export const checkListData = (params?: any) =>
  defHttp.post<M8OperCheck>({ url: adminPath + '/order/m8OperCheck/listCheckSelect', params });
