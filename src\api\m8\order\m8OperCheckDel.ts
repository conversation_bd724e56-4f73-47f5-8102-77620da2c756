/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface M8OperCheckDel extends BasicModel<M8OperCheckDel> {
  djNo?: string; // 处理单号
  checkId?: string; // 检验单ID
  operCode?: string; // 责任工序
  iqty?: number; // 处理数量
  isNeedYf?: string; // 是否需要纠正预防措施
  isNeedCus?: string; // 是否需要客户审理
  checkRemarks?: string; // 不合格品描述
  checkor?: string; // 检验员
  checkorName?: string; // 检验员姓名
  operReson?: string; // 原因分析
  operator?: string; // 操作员
  operatorName?: string; // 操作员姓名
  operatorSubDate?: string; // 操作员确认日期
  dealType?: string; // 处理意见类型
  dealRemarks?: string; // 处理方案
  resultManager?: string; // 处理方案和结果负责人
  resultManagerName?: string; // 处理方案和结果负责人
  dealResultSunDate?: string; // 处理方案确认日期
  lastDealResult?: string; // 最终处理意见
  lastSubResultType?: string; // 最终结果
  lastSubCheckor?: string; // 最终检验人员
  lastSubCheckorName?: string; // 最终检验人员
  lastSubCheckDate?: string; // 最终检验合格日期
  rsTime?: number; // 返修工时
  rsMoney?: number; // 返修金额
  isDedPoint?: string; // 是否扣分
  dealStatus?: string; // 当前阶段
  order?: any; // 订单
  orderPic?: any; // 图纸
}

export const m8OperCheckDelList = (params?: M8OperCheckDel | any) =>
  defHttp.get<M8OperCheckDel>({ url: adminPath + '/check/m8OperCheckDel/list', params });

export const m8OperCheckDelListData = (params?: M8OperCheckDel | any) =>
  defHttp.post<Page<M8OperCheckDel>>({ url: adminPath + '/check/m8OperCheckDel/listData', params });

export const m8OperCheckDelForm = (params?: M8OperCheckDel | any) =>
  defHttp.get<M8OperCheckDel>({ url: adminPath + '/check/m8OperCheckDel/form', params });

export const m8OperCheckDelSave = (params?: any, data?: M8OperCheckDel | any) =>
  defHttp.postJson<M8OperCheckDel>({ url: adminPath + '/check/m8OperCheckDel/save', params, data });

export const m8OperCheckDelDelete = (params?: M8OperCheckDel | any) =>
  defHttp.get<M8OperCheckDel>({ url: adminPath + '/check/m8OperCheckDel/delete', params });

export const stageConfirm = (params?: M8OperCheckDel | any) =>
  defHttp.get<M8OperCheckDel>({ url: adminPath + '/check/m8OperCheckDel/stageConfirm', params });

// 责任工序
export const listTreeData = (params?: M8OperCheckDel | any) =>
  defHttp.post<M8OperCheckDel>({
    url: adminPath + '/m8/order/flow/check/m8OrderPicFlowCheck/listTreeData',
    params,
  });
