/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface M8OrderFlowWxIn extends BasicModel<M8OrderFlowWxIn> {
  djno?: string; // 单据号
  outDjno?: string; // 发料单号
  ddate?: string; // 日期
  venCode?: string; // 厂商
  receiver?: string; // 收料人
  auditor?: string; // 审核人
  auditDate?: string; // 审核时间
  inStatus?: string; // 状态
  m8OrderFlowWxInRdsList?: any[]; // 子表列表
  allowEdit?: boolean; //是否可编辑
  allowDel?: boolean; //是否可删除
}

export const m8OrderFlowWxInList = (params?: M8OrderFlowWxIn | any) =>
  defHttp.get<M8OrderFlowWxIn>({
    url: adminPath + '/m8/order/flow/wx/m8OrderFlowWxIn/list',
    params,
  });

export const m8OrderFlowWxInListData = (params?: M8OrderFlowWxIn | any) =>
  defHttp.post<Page<M8OrderFlowWxIn>>({
    url: adminPath + '/m8/order/flow/wx/m8OrderFlowWxIn/listData',
    params,
  });

export const m8OrderFlowWxInForm = (params?: M8OrderFlowWxIn | any) =>
  defHttp.get<M8OrderFlowWxIn>({
    url: adminPath + '/m8/order/flow/wx/m8OrderFlowWxIn/form',
    params,
  });

export const m8OrderFlowWxInSave = (params?: any, data?: M8OrderFlowWxIn | any) =>
  defHttp.postJson<M8OrderFlowWxIn>({
    url: adminPath + '/m8/order/flow/wx/m8OrderFlowWxIn/save',
    params,
    data,
  });

export const m8OrderFlowWxInDelete = (params?: M8OrderFlowWxIn | any) =>
  defHttp.get<M8OrderFlowWxIn>({
    url: adminPath + '/m8/order/flow/wx/m8OrderFlowWxIn/delete',
    params,
  });

export const m8OrderFlowWxInSp = (params?: M8OrderFlowWxIn | any) =>
  defHttp.get<M8OrderFlowWxIn>({
    url: adminPath + '/m8/order/flow/wx/m8OrderFlowWxIn/batchSp',
    params,
  });

export const m8OrderFlowWxInDeSp = (params?: M8OrderFlowWxIn | any) =>
  defHttp.get<M8OrderFlowWxIn>({
    url: adminPath + '/m8/order/flow/wx/m8OrderFlowWxIn/batchDeSp',
    params,
  });

// 自定义列表接口
export const listSet = (params?: any) => defHttp.post({ url: adminPath + '/bas/listSet', params });

// 自定义列表接口
export const formSet = (params?: any) => defHttp.post({ url: adminPath + '/bas/formSet', params });

// customListData接口
export const customListData = (params?: any) =>
  defHttp.post<Page<M8OrderFlowWxIn>>({ url: adminPath + params.url, params });
