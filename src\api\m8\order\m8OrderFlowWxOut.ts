/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface M8OrderFlowWxOut extends BasicModel<M8OrderFlowWxOut> {
  m8OrderFlowWxOut: {};
  djno?: string; // 单据号
  ddate?: string; // 日期
  venCode?: string; // 厂商
  outUser?: string; // 发料人
  auditor?: string; // 审核人
  auditDate?: string; // 审核时间
  logisticsNo?: string; // 物流单号
  logistics?: string; // 物流公司
  receiver?: string; // 收料人
  receiveAddress?: string; // 收料地址
  iphone?: string; // 联系方式
  outStatus?: string; // 状态
  bred?: string; // 红蓝标志
  m8OrderFlowWxOutRdsList?: any[]; // 子表列表
  isNewRecord?: boolean; // 是否新增
  allowEdit?: boolean; // 是否可编辑
  allowDel?: boolean; // 是否可删除
}

export const m8OrderFlowWxOutList = (params?: M8OrderFlowWxOut | any) =>
  defHttp.get<M8OrderFlowWxOut>({
    url: adminPath + '/m8/order/flow/wx/m8OrderFlowWxOut/list',
    params,
  });

export const m8OrderFlowWxOutListData = (params?: M8OrderFlowWxOut | any) =>
  defHttp.post<Page<M8OrderFlowWxOut>>({
    url: adminPath + '/m8/order/flow/wx/m8OrderFlowWxOut/listData',
    params,
  });

export const subSelectList = (params?: any) =>
  defHttp.post<Page<M8OrderFlowWxOut>>({
    url: adminPath + '/m8/order/flow/wx/m8OrderFlowWxOut/subSelectList',
    params,
  });

export const m8OrderFlowWxOutForm = (params?: M8OrderFlowWxOut | any) =>
  defHttp.get<M8OrderFlowWxOut>({
    url: adminPath + '/m8/order/flow/wx/m8OrderFlowWxOut/form',
    params,
  });

export const m8OrderFlowWxOutFormRed = (params?: M8OrderFlowWxOut | any) =>
  defHttp.get<M8OrderFlowWxOut>({
    url: adminPath + '/m8/order/flow/wx/m8OrderFlowWxOut/formRed',
    params,
  });

export const m8OrderFlowWxOutSave = (params?: any, data?: M8OrderFlowWxOut | any) =>
  defHttp.postJson<M8OrderFlowWxOut>({
    url: adminPath + '/m8/order/flow/wx/m8OrderFlowWxOut/save',
    params,
    data,
  });

export const m8OrderFlowWxOutSaveRed = (params?: any, data?: M8OrderFlowWxOut | any) =>
  defHttp.postJson<M8OrderFlowWxOut>({
    url: adminPath + '/m8/order/flow/wx/m8OrderFlowWxOut/saveRed',
    params,
    data,
  });

export const m8OrderFlowWxOutDelete = (params?: M8OrderFlowWxOut | any) =>
  defHttp.get<M8OrderFlowWxOut>({
    url: adminPath + '/m8/order/flow/wx/m8OrderFlowWxOut/delete',
    params,
  });

export const m8OrderFlowWxOutSp = (params?: M8OrderFlowWxOut | any) =>
  defHttp.get<M8OrderFlowWxOut>({
    url: adminPath + '/m8/order/flow/wx/m8OrderFlowWxOut/batchSp',
    params,
  });

export const m8OrderFlowWxOutDeSp = (params?: M8OrderFlowWxOut | any) =>
  defHttp.get<M8OrderFlowWxOut>({
    url: adminPath + '/m8/order/flow/wx/m8OrderFlowWxOut/batchDeSp',
    params,
  });

// 自定义列表接口
export const listSet = (params?: any) => defHttp.post({ url: adminPath + '/bas/listSet', params });

// 自定义列表接口
export const formSet = (params?: any) => defHttp.post({ url: adminPath + '/bas/formSet', params });

// customListData接口
export const customListData = (params?: any) =>
  defHttp.post<Page<M8OrderFlowWxOut>>({ url: adminPath + params.url, params });
