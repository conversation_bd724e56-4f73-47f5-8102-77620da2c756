/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../model/baseModel';
import { M8OrderPicFlowOver } from '../flow/flow';

const { adminPath } = useGlobSetting();

export type Order = BasicModel<Order>;

// export const testDataList = (params?: TestData | any) =>
//   defHttp.get<TestData>({ url: adminPath + '/test/testData/list', params });

// export const testDataListData = (params?: TestData | any) =>
//   defHttp.post<Page<TestData>>({ url: adminPath + '/test/testData/listData', params });

// export const testDataForm = (params?: TestData | any) =>
//   defHttp.get<TestData>({ url: adminPath + '/test/testData/form', params });

// export const testDataSave = (params?: any, data?: TestData | any) =>
//   defHttp.postJson<TestData>({ url: adminPath + '/test/testData/save', params, data });

// export const testDataDisable = (params?: TestData | any) =>
//   defHttp.get<TestData>({ url: adminPath + '/test/testData/disable', params });

// export const testDataEnable = (params?: TestData | any) =>
//   defHttp.get<TestData>({ url: adminPath + '/test/testData/enable', params });

// export const testDataDelete = (params?: TestData | any) =>
//   defHttp.get<TestData>({ url: adminPath + '/test/testData/delete', params });

// export const orderTaskListData = (params?: Order | any) =>
//   defHttp.post<Page<Order>>({ url: adminPath + '/m8/machine/task/orderTaskListData', params });

// export const taskLastNodeData = (params?: Order | any) =>
//   defHttp.post<Page<Order>>({ url: adminPath + '/m8/machine/task/taskLastNodeData', params });

export const overOrder = (params?: Order | any) =>
  defHttp.get<Order>({ url: adminPath + '/m8/order/overOrder', params });

export const updateFprice = (params?: Order | any) =>
  defHttp.post<Order>({ url: adminPath + '/m8/order/flow/updateFprice', params });

export const updateManager = (params?: Order | any) =>
  defHttp.post<Order>({ url: adminPath + '/m8/order/flow/updateManager', params });

export const updateHfDate = (params?: Order | any) =>
  defHttp.post<Order>({ url: adminPath + '/m8/order/flow/updateHfDate', params });

export const updateLastDate = (params?: Order | any) =>
  defHttp.post<Order>({ url: adminPath + '/m8/order/flow/updateLastDate', params });

export const fileList = (params?: any) =>
  defHttp.get<Order>({ url: adminPath + '/file/fileList', params });

export const clear = (params?: any) =>
  defHttp.get<Order>({ url: adminPath + '/m8/order/flow/clear', params });

export const overSave = (params?: any, data?: M8OrderPicFlowOver | any) =>
  defHttp.postJson<M8OrderPicFlowOver>({
    url: adminPath + '/m8/order/flow/over/save',
    params,
    data,
  });

export const batchSave = (params?: any) =>
  defHttp.post<M8OrderPicFlowOver>({ url: adminPath + '/m8/order/flow/over/batchSave', params });

export const orderSxDelete = (params?: Order | any) =>
  defHttp.get<Order>({ url: adminPath + '/m8/order/sx/delete', params });

export const orderSxForm = (params?: Order | any) =>
  defHttp.get<Order>({ url: adminPath + '/m8/order/sx/form', params });

export const orderSxUpdateFprice = (params?: Order | any) =>
  defHttp.get<Order>({ url: adminPath + '/m8/order/sx/updateFprice', params });

// 截止日期按钮接口
export const btnUpdateJzDate = (params?: Order | any) =>
  defHttp.post<Order>({ url: adminPath + '/m8/order/updateJzDate', params });

export const btnUpdateHfDate = (params?: Order | any) =>
  defHttp.get<Order>({ url: adminPath + '/m8/order/updateHfDate', params });

export const btnUpdateOrderStatus = (params?: Order | any) =>
  defHttp.post<Order>({ url: adminPath + '/m8/order/updateOrderStatus', params });

export const btnEnabelOrder = (params?: Order | any) =>
  defHttp.post<Order>({ url: adminPath + '/m8/order/enabelOrder', params });

export const btnUpdateYgOverDate = (params?: Order | any) =>
  defHttp.get<Order>({ url: adminPath + '/m8/order/updateYgOverDate', params });

export const moreSave = (params?: any) =>
  defHttp.post<Order>({ url: adminPath + '/m8/order/flow/more/save', params });

export const orderStatusList = (params?: any) =>
  defHttp.post<any>({
    url: adminPath + '/m8/order/orderStatusList',
    params,
  });

export const orderStatusDetailsList = (params?: any) =>
  defHttp.post<any>({
    url: adminPath + '/m8/order/orderStatusDetailsList',
    params,
  });

//修改订单紧急状态
export const changeStatus = (params?: Order | any) =>
  defHttp.post<Order>({ url: adminPath + '/m8/order/changeJJLevel', params });
