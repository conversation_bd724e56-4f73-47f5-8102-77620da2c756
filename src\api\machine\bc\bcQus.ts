import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../model/baseModel';

const { adminPath } = useGlobSetting();

export interface BCQus extends BasicModel<BCQus> {
  orderPic?: any;
}

export const bcQusForm = (params?: BCQus | any) =>
  defHttp.get<BCQus>({ url: adminPath + '/m8/order/flow/bcQus/form', params });

export const bcQusSave = (params?: any, data?: BCQus | any) =>
  defHttp.postJson<BCQus>({ url: adminPath + '/m8/order/flow/bcQus/save', params, data });

export const bcQusUpdate = (params?: any, data?: BCQus | any) =>
  defHttp.postJson<BCQus>({ url: adminPath + '/m8/order/flow/bcQus/updateQrData', params, data });

export const bcQusDelete = (params?: BCQus | any) =>
  defHttp.post<BCQus>({ url: adminPath + '/m8/order/flow/bcQus/delete', params });
