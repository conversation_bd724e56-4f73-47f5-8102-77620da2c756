import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

export const { adminPath } = useGlobSetting();

export interface Machine extends BasicModel<Machine> {
  routeCode?: string; // 单行文本
  routeName?: string; // 单行文本
  remarks?: string; // 单行文本
  operList?: any[]; // 子表列表
}

export const listData = (params?: any) =>
  defHttp.get<any>({ url: adminPath + '/m8/machine/listData', params }); //这个路径是后台的路径，主表数据

export const routeDelete = (params?: any) =>
  defHttp.get<any>({ url: adminPath + '/bas/route/delete', params }); //主表的删除接口

export const routeChildListData = (params?: any) =>
  defHttp.get<any>({ url: adminPath + '/bas/route/subListData', params }); //子表的数据接口

export const routeSaveListData = (params?: any, data?: Machine | any) =>
  defHttp.postJson<any>({ url: adminPath + '/bas/route/save', params, data }); //主表的编辑数据接口

export const routeForm = (params?: Machine | any) =>
  defHttp.get<Machine>({ url: adminPath + '/bas/route/form', params }); //表单中主表的数据接口

export const operTreeData = (params?: Machine | any) =>
  defHttp.get<Machine>({
    url: adminPath + '/bas/oper/treeData?isShowCode=true',
    params,
  }); //表单中主表的数据接口

// 自定义列表接口
export const listSet = (params?: any) => defHttp.post({ url: adminPath + '/bas/listSet', params });

// 自定义列表接口
export const formSet = (params?: any) => defHttp.post({ url: adminPath + '/bas/formSet', params });

// customListData接口
export const customListData = (params?: any) =>
  defHttp.post<Page<Machine>>({ url: adminPath + params.url, params });

// export const routeSaveListData = (params?: any, data?: Machine | any) =>
// defHttp.postJson<any>({ url: adminPath + '/m8/machine/over/taskOver/save', params, data }); //主表的编辑数据接口
export const taskOvesave = (params?: any) =>
  defHttp.post<Machine>({ url: adminPath + '/m8/machine/over/save', params });
