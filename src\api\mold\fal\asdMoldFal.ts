/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface AsdMoldFal extends BasicModel<AsdMoldFal> {
  hid?: string; // 维修记录ID
  moldCode?: string; // 模具编码
  lastNum?: string; // 模具尾号
  operCode?: string; // 工序编码
  cinvcode?: string; // 存货编码
  falStatus?: string; // 确认状态
  createByName?: string; // 确认人名称
}

export const asdMoldFalList = (params?: AsdMoldFal | any) =>
  defHttp.get<AsdMoldFal>({ url: adminPath + '/mold/fal/asdMoldFal/list', params });

export const asdMoldFalListData = (params?: AsdMoldFal | any) =>
  defHttp.post<Page<AsdMoldFal>>({ url: adminPath + '/mold/fal/asdMoldFal/listData', params });

export const asdMoldFalForm = (params?: AsdMoldFal | any) =>
  defHttp.get<AsdMoldFal>({ url: adminPath + '/mold/fal/asdMoldFal/form', params });

export const asdMoldFalSave = (params?: any, data?: AsdMoldFal | any) =>
  defHttp.postJson<AsdMoldFal>({ url: adminPath + '/mold/fal/asdMoldFal/save', params, data });

export const asdMoldFalDelete = (params?: AsdMoldFal | any) =>
  defHttp.get<AsdMoldFal>({ url: adminPath + '/mold/fal/asdMoldFal/delete', params });
