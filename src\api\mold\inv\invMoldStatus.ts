/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface InvMoldStatus extends BasicModel<InvMoldStatus> {
  cinvaddcode?: string; // 存货代码
  cinvcode?: string; // 存货编码
  cinvname?: string; // 存货名称
  caddcode?: string; // 工装代码
  code?: string; // 工装编码
  lastnum?: string; // 工装尾号
  name?: string; // 工装名称
  moldStatus?: string; // 状态
}

export const invMoldStatusList = (params?: InvMoldStatus | any) =>
  defHttp.get<InvMoldStatus>({ url: adminPath + '/mold/inv/invMoldStatus/list', params });

export const invMoldStatusListData = (params?: InvMoldStatus | any) =>
  defHttp.post<Page<InvMoldStatus>>({ url: adminPath + '/mold/inv/invMoldStatus/listData', params });

export const invMoldStatusForm = (params?: InvMoldStatus | any) =>
  defHttp.get<InvMoldStatus>({ url: adminPath + '/mold/inv/invMoldStatus/form', params });

export const invMoldStatusSave = (params?: any, data?: InvMoldStatus | any) =>
  defHttp.postJson<InvMoldStatus>({ url: adminPath + '/mold/inv/invMoldStatus/save', params, data });

export const invMoldStatusImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/mold/inv/invMoldStatus/importData',
      onUploadProgress,
    },
    params,
  );

export const invMoldStatusDelete = (params?: InvMoldStatus | any) =>
  defHttp.get<InvMoldStatus>({ url: adminPath + '/mold/inv/invMoldStatus/delete', params });
