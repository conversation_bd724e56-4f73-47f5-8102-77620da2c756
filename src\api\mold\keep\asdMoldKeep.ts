/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { BasMold } from '../model/basMold';

const { adminPath } = useGlobSetting();

export interface AsdMoldKeep extends BasicModel<AsdMoldKeep> {
  isNewRecord?: boolean; // 是否新增
  moldCode?: string; // 模具编码
  lastNum?: string; // 模具尾号
  keepDesc?: string; // 保养描述
  createByName?: string; // 保养人
  asdMoldKeep?: AsdMoldKeep; // 关联模具
  basMold?: BasMold; // 关联工装
  __t?: number; // 时间戳
  id: string; // 主键
  status: string; // 状态
  message: string; // 消息
}

export const asdMoldKeepList = (params?: AsdMoldKeep | any) =>
  defHttp.get<AsdMoldKeep>({ url: adminPath + '/mold/keep/asdMoldKeep/list', params });

export const asdMoldKeepListData = (params?: AsdMoldKeep | any) =>
  defHttp.post<Page<AsdMoldKeep>>({ url: adminPath + '/mold/keep/asdMoldKeep/listData', params });

export const asdMoldKeepForm = (params?: AsdMoldKeep | any) =>
  defHttp.get<AsdMoldKeep>({ url: adminPath + '/mold/keep/asdMoldKeep/form', params });

export const asdMoldKeepSave = (params?: any, data?: AsdMoldKeep | any) =>
  defHttp.postJson<AsdMoldKeep>({ url: adminPath + '/mold/keep/asdMoldKeep/save', params, data });

export const asdMoldKeepDelete = (params?: AsdMoldKeep | any) =>
  defHttp.get<AsdMoldKeep>({ url: adminPath + '/mold/keep/asdMoldKeep/delete', params });
