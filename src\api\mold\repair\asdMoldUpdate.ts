/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface AsdMoldUpdate extends BasicModel<AsdMoldUpdate> {
  moldCode?: string; // 模具编码
  lastNum?: string; // 模具尾号
  operCode?: string; // 工序编码
  ddate?: string; // 登记日期
  devName?: string; // 设备名称
  repairDate?: string; // 维修日期
  repairDescription?: string; // 维修描述
}

export const asdMoldUpdateList = (params?: AsdMoldUpdate | any) =>
  defHttp.get<AsdMoldUpdate>({ url: adminPath + '/mold/repair/asdMoldUpdate/list', params });

export const asdMoldUpdateListData = (params?: AsdMoldUpdate | any) =>
  defHttp.post<Page<AsdMoldUpdate>>({
    url: adminPath + '/mold/repair/asdMoldUpdate/listData',
    params,
  });

export const asdMoldUpdateForm = (params?: AsdMoldUpdate | any) =>
  defHttp.get<AsdMoldUpdate>({ url: adminPath + '/mold/repair/asdMoldUpdate/form', params });

export const asdMoldUpdateSave = (params?: any, data?: AsdMoldUpdate | any) =>
  defHttp.postJson<AsdMoldUpdate>({
    url: adminPath + '/mold/repair/asdMoldUpdate/save',
    params,
    data,
  });

export const asdMoldUpdateDelete = (params?: AsdMoldUpdate | any) =>
  defHttp.get<AsdMoldUpdate>({ url: adminPath + '/mold/repair/asdMoldUpdate/delete', params });
