/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface VmoldKeepDay extends BasicModel<VmoldKeepDay> {
  ddate?: string; // 登记机日期(天)
  keepDesc?: string; // 模具保养项目
  caddcode?: string; // 工装代码
  code?: string; // 工装编码
  lastnum?: string; // 工装编码尾号
  name?: string; // 工装名称
  userName?: string; // 保养人员
  keepTime?: number; // 保养时间
  cinvaddcode?: string; // 加工产品
  iqty?: number; // 加工数量
  devName?: string; // 设备编码
  operCode?: string; // 工序
}

export const vmoldKeepDayList = (params?: VmoldKeepDay | any) =>
  defHttp.get<VmoldKeepDay>({ url: adminPath + '/report/vmoldKeepDay/list', params });

export const vmoldKeepDayListData = (params?: VmoldKeepDay | any) =>
  defHttp.post<Page<VmoldKeepDay>>({ url: adminPath + '/report/vmoldKeepDay/listData', params });

export const vmoldKeepDayForm = (params?: VmoldKeepDay | any) =>
  defHttp.get<VmoldKeepDay>({ url: adminPath + '/report/vmoldKeepDay/form', params });

export const vmoldKeepDaySave = (params?: any, data?: VmoldKeepDay | any) =>
  defHttp.postJson<VmoldKeepDay>({ url: adminPath + '/report/vmoldKeepDay/save', params, data });

export const vmoldKeepDayImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/report/vmoldKeepDay/importData',
      onUploadProgress,
    },
    params,
  );

export const vmoldKeepDayDelete = (params?: VmoldKeepDay | any) =>
  defHttp.get<VmoldKeepDay>({ url: adminPath + '/report/vmoldKeepDay/delete', params });
