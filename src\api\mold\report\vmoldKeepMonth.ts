/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface VmoldKeepMonth extends BasicModel<VmoldKeepMonth> {
  ddate?: string; // 登记日期(月)
  keepDesc?: string; // 模具保养项目
  caddcode?: string; // 工装代码
  code?: string; // 工装编码
  lastnum?: string; // 工装编码尾号
  name?: string; // 工装名称
  userName?: string; // 保养人员
  keepTime?: number; // 保养时间
  cinvaddcode?: string; // 加工产品
  iqty?: number; // 加工数量
  devName?: string; // 设备编码
  operCode?: string; // 工序
}

export const vmoldKeepMonthList = (params?: VmoldKeepMonth | any) =>
  defHttp.get<VmoldKeepMonth>({ url: adminPath + '/report/vmoldKeepMonth/list', params });

export const vmoldKeepMonthListData = (params?: VmoldKeepMonth | any) =>
  defHttp.post<Page<VmoldKeepMonth>>({ url: adminPath + '/report/vmoldKeepMonth/listData', params });

export const vmoldKeepMonthForm = (params?: VmoldKeepMonth | any) =>
  defHttp.get<VmoldKeepMonth>({ url: adminPath + '/report/vmoldKeepMonth/form', params });

export const vmoldKeepMonthSave = (params?: any, data?: VmoldKeepMonth | any) =>
  defHttp.postJson<VmoldKeepMonth>({ url: adminPath + '/report/vmoldKeepMonth/save', params, data });

export const vmoldKeepMonthImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/report/vmoldKeepMonth/importData',
      onUploadProgress,
    },
    params,
  );

export const vmoldKeepMonthDelete = (params?: VmoldKeepMonth | any) =>
  defHttp.get<VmoldKeepMonth>({ url: adminPath + '/report/vmoldKeepMonth/delete', params });
