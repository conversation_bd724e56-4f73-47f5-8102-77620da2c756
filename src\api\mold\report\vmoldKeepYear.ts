/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface VmoldKeepYear extends BasicModel<VmoldKeepYear> {
  ddate?: string; // 登记日期(年)
  keepDesc?: string; // 模具保养项目
  caddcode?: string; // 工装代码
  code?: string; // 工装编码
  lastnum?: string; // 工装编码尾号
  name?: string; // 工装名称
  userName?: string; // 保养人员
  keepTime?: number; // 保养时间
  cinvaddcode?: string; // 加工产品
  iqty?: number; // 加工数量
  devName?: string; // 设备编码
  operCode?: string; // 工序
}

export const vmoldKeepYearList = (params?: VmoldKeepYear | any) =>
  defHttp.get<VmoldKeepYear>({ url: adminPath + '/report/vmoldKeepYear/list', params });

export const vmoldKeepYearListData = (params?: VmoldKeepYear | any) =>
  defHttp.post<Page<VmoldKeepYear>>({ url: adminPath + '/report/vmoldKeepYear/listData', params });

export const vmoldKeepYearForm = (params?: VmoldKeepYear | any) =>
  defHttp.get<VmoldKeepYear>({ url: adminPath + '/report/vmoldKeepYear/form', params });

export const vmoldKeepYearSave = (params?: any, data?: VmoldKeepYear | any) =>
  defHttp.postJson<VmoldKeepYear>({ url: adminPath + '/report/vmoldKeepYear/save', params, data });

export const vmoldKeepYearImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/report/vmoldKeepYear/importData',
      onUploadProgress,
    },
    params,
  );

export const vmoldKeepYearDelete = (params?: VmoldKeepYear | any) =>
  defHttp.get<VmoldKeepYear>({ url: adminPath + '/report/vmoldKeepYear/delete', params });
