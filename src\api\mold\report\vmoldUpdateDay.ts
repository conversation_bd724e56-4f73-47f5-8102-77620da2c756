/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface VmoldUpdateDay extends BasicModel<VmoldUpdateDay> {
  repairDescription?: string; // 模具维修项目
  caddcode?: string; // 工装代码
  code?: string; // 工装编码
  lastnum?: string; // 工装编码尾号
  name?: string; // 工装名称
  repairDate?: string; // 维修日期
  userName?: string; // 维修人员
  repairTime?: number; // 维修时间
  cinvaddcode?: string; // 加工产品
  iqty?: number; // 加工数量
  devName?: string; // 设备编码
  operCode?: string; // 工序
}

export const vmoldUpdateDayList = (params?: VmoldUpdateDay | any) =>
  defHttp.get<VmoldUpdateDay>({ url: adminPath + '/report/vmoldUpdateDay/list', params });

export const vmoldUpdateDayListData = (params?: VmoldUpdateDay | any) =>
  defHttp.post<Page<VmoldUpdateDay>>({ url: adminPath + '/report/vmoldUpdateDay/listData', params });

export const vmoldUpdateDayForm = (params?: VmoldUpdateDay | any) =>
  defHttp.get<VmoldUpdateDay>({ url: adminPath + '/report/vmoldUpdateDay/form', params });

export const vmoldUpdateDaySave = (params?: any, data?: VmoldUpdateDay | any) =>
  defHttp.postJson<VmoldUpdateDay>({ url: adminPath + '/report/vmoldUpdateDay/save', params, data });

export const vmoldUpdateDayImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/report/vmoldUpdateDay/importData',
      onUploadProgress,
    },
    params,
  );

export const vmoldUpdateDayDelete = (params?: VmoldUpdateDay | any) =>
  defHttp.get<VmoldUpdateDay>({ url: adminPath + '/report/vmoldUpdateDay/delete', params });
