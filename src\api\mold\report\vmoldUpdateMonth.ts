/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface VmoldUpdateMonth extends BasicModel<VmoldUpdateMonth> {
  ddate?: string; // 日期（月）
  repairDescription?: string; // 模具维修项目
  caddcode?: string; // 工装代码
  code?: string; // 工装编码
  lastnum?: string; // 工装编码尾号
  name?: string; // 工装名称
  repairDate?: string; // 维修日期
  userName?: string; // 维修人员
  repairTime?: number; // 维修时间
  cinvaddcode?: string; // 加工产品
  iqty?: number; // 加工数量
  devName?: string; // 设备编码
  operCode?: string; // 工序
}

export const vmoldUpdateMonthList = (params?: VmoldUpdateMonth | any) =>
  defHttp.get<VmoldUpdateMonth>({ url: adminPath + '/report/vmoldUpdateMonth/list', params });

export const vmoldUpdateMonthListData = (params?: VmoldUpdateMonth | any) =>
  defHttp.post<Page<VmoldUpdateMonth>>({ url: adminPath + '/report/vmoldUpdateMonth/listData', params });

export const vmoldUpdateMonthForm = (params?: VmoldUpdateMonth | any) =>
  defHttp.get<VmoldUpdateMonth>({ url: adminPath + '/report/vmoldUpdateMonth/form', params });

export const vmoldUpdateMonthSave = (params?: any, data?: VmoldUpdateMonth | any) =>
  defHttp.postJson<VmoldUpdateMonth>({ url: adminPath + '/report/vmoldUpdateMonth/save', params, data });

export const vmoldUpdateMonthImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/report/vmoldUpdateMonth/importData',
      onUploadProgress,
    },
    params,
  );

export const vmoldUpdateMonthDelete = (params?: VmoldUpdateMonth | any) =>
  defHttp.get<VmoldUpdateMonth>({ url: adminPath + '/report/vmoldUpdateMonth/delete', params });
