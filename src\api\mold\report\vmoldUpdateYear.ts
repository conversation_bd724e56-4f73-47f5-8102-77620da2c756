/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface VmoldUpdateYear extends BasicModel<VmoldUpdateYear> {
  ddate?: string; // 日期（年）
  repairDescription?: string; // 模具维修项目
  caddcode?: string; // 工装代码
  code?: string; // 工装编码
  lastnum?: string; // 工装编码尾号
  name?: string; // 工装名称
  repairDate?: string; // 维修日期
  userName?: string; // 维修人员
  repairTime?: number; // 维修时间
  cinvaddcode?: string; // 加工产品
  iqty?: number; // 加工数量
  devName?: string; // 设备编码
  operCode?: string; // 工序
}

export const vmoldUpdateYearList = (params?: VmoldUpdateYear | any) =>
  defHttp.get<VmoldUpdateYear>({ url: adminPath + '/report/vmoldUpdateYear/list', params });

export const vmoldUpdateYearListData = (params?: VmoldUpdateYear | any) =>
  defHttp.post<Page<VmoldUpdateYear>>({ url: adminPath + '/report/vmoldUpdateYear/listData', params });

export const vmoldUpdateYearForm = (params?: VmoldUpdateYear | any) =>
  defHttp.get<VmoldUpdateYear>({ url: adminPath + '/report/vmoldUpdateYear/form', params });

export const vmoldUpdateYearSave = (params?: any, data?: VmoldUpdateYear | any) =>
  defHttp.postJson<VmoldUpdateYear>({ url: adminPath + '/report/vmoldUpdateYear/save', params, data });

export const vmoldUpdateYearImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/report/vmoldUpdateYear/importData',
      onUploadProgress,
    },
    params,
  );

export const vmoldUpdateYearDelete = (params?: VmoldUpdateYear | any) =>
  defHttp.get<VmoldUpdateYear>({ url: adminPath + '/report/vmoldUpdateYear/delete', params });
