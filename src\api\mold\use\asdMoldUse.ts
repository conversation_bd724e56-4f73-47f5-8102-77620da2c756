/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface AsdMoldUse extends BasicModel<AsdMoldUse> {
  moldCode?: string; // 模具编码
  operCode?: string; // 工序编码
  invCode?: string; // 产品编码
  devName?: string; // 设备名称
  iqty?: number; // 使用次数
  stratDate?: string; // 开始时间
  endDate?: string; // 结束日期
}

export const asdMoldUseList = (params?: AsdMoldUse | any) =>
  defHttp.get<AsdMoldUse>({ url: adminPath + '/mold/use/asdMoldUse/list', params });

export const asdMoldUseListData = (params?: AsdMoldUse | any) =>
  defHttp.post<Page<AsdMoldUse>>({ url: adminPath + '/mold/use/asdMoldUse/listData', params });

export const asdMoldUseForm = (params?: AsdMoldUse | any) =>
  defHttp.get<AsdMoldUse>({ url: adminPath + '/mold/use/asdMoldUse/form', params });

export const asdMoldUseSave = (params?: any, data?: AsdMoldUse | any) =>
  defHttp.postJson<AsdMoldUse>({ url: adminPath + '/mold/use/asdMoldUse/save', params, data });

export const asdMoldUseDelete = (params?: AsdMoldUse | any) =>
  defHttp.get<AsdMoldUse>({ url: adminPath + '/mold/use/asdMoldUse/delete', params });
