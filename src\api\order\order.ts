/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export type Order = BasicModel<Order>;

export interface M8OrderPic extends BasicModel<M8OrderPic> {
  picno?: string; // 图号
  picname?: string; // 图名
  cinvstd?: string; // 规格
  order?: any; // 订单
  flowList?: any[]; // 子表列表
}

export const orderDelete = (params?: Order | any) =>
  defHttp.get<Order>({ url: adminPath + '/m8/order/delete', params });

export const orderForm = (params?: Order | any) =>
  defHttp.get<Order>({ url: adminPath + '/m8/order/form', params });

export const orderSave = (params?: any, data?: Order | any) =>
  defHttp.postJson<Order>({ url: adminPath + '/m8/order/save', params, data });

export const orderFlowListData = (params?: Order | any) =>
  defHttp.post<Page<Order>>({ url: adminPath + '/m8/order/flow/listData', params });

// 图纸信息

export const orderPicForm = (params?: Order | any) =>
  defHttp.get<Order>({ url: adminPath + '/m8/order/pic/form', params });

export const orderPicDelete = (params?: Order | any) =>
  defHttp.get<Order>({ url: adminPath + '/m8/order/pic/delete', params });

export const orderPicSave = (params?: any, data?: Order | any) =>
  defHttp.postJson<Order>({ url: adminPath + '/m8/order/pic/save', params, data });

export const addRemarks = (params?: Order | any) =>
  defHttp.post<Order>({ url: adminPath + '/m8/order/pic/addRemarks', params });

export const bachEditPrice = (params?: Order | any) =>
  defHttp.post<Order>({ url: adminPath + '/m8/order/pic/bachEditPrice', params });

export const copyExcel = (params?: Order | any) =>
  defHttp.post<Order>({ url: adminPath + '/m8/order/pic/copyExcel', params });

export const findAllRemarks = (params?: Order | any) =>
  defHttp.get<Order>({ url: adminPath + '/m8/order/pic/findAllRemarks', params });

//流程截止时间批量更新FORM
export const updateLastDateForm = (params?: M8OrderPic | any) =>
  defHttp.get<M8OrderPic>({ url: adminPath + '/m8/order/flow/updateLastDateForm', params });
//流程截止时间批量更新SAVE
export const updateLastDateSave = (params?: any, data?: M8OrderPic | any) =>
  defHttp.postJson<M8OrderPic>({
    url: adminPath + '/m8/order/flow/updateLastDate_batch',
    params,
    data,
  });
