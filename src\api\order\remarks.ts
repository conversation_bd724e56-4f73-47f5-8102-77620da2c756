import { BasicModel } from '../model/baseModel';
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';

const { adminPath } = useGlobSetting();

export interface Remarks extends BasicModel<Remarks> {
  remarks?: string; // 单行文本
  groupName?: string; // 单行文本
  createDate?: Date;
  operCode?: string;
  machineName?: string;
}

export const remarkData = (params?: any) =>
  defHttp.get<any>({ url: adminPath + '/bas/route/listData', params }); //暂时没得接口
