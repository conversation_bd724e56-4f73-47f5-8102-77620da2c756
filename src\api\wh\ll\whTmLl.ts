/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface WhTmLl extends BasicModel<WhTmLl> {
  djno?: string; // 任务号
  cwhcode?: string; // 发料仓库
  xqcwhcode?: string; // 需求仓库
  cinvcode?: string; // 存货
  frate?: number; // 比例
  iqty?: number; // 数量
  cposcode?: string; // 货位信息
  pickQty?: number; // 领料数量
  pickBy?: string; // 领料人
  area?: string; // 区域
  xqDate?: string; // 需求时间
}

export const whTmLlList = (params?: WhTmLl | any) =>
  defHttp.get<WhTmLl>({ url: adminPath + '/wh/ll/whTmLl/list', params });

export const whTmLlListData = (params?: WhTmLl | any) =>
  defHttp.post<Page<WhTmLl>>({ url: adminPath + '/wh/ll/whTmLl/listData', params });

export const whTmLlForm = (params?: WhTmLl | any) =>
  defHttp.get<WhTmLl>({ url: adminPath + '/wh/ll/whTmLl/form', params });

export const whTmLlSave = (params?: any, data?: WhTmLl | any) =>
  defHttp.postJson<WhTmLl>({ url: adminPath + '/wh/ll/whTmLl/save', params, data });

export const whTmLlDelete = (params?: WhTmLl | any) =>
  defHttp.get<WhTmLl>({ url: adminPath + '/wh/ll/whTmLl/delete', params });

export const fileDataUpload = (params?: WhTmLl | any) =>
  defHttp.get<WhTmLl>({
    url: adminPath + '/wh/ll/whTmLl/fileDataUpload',
    params,
  });

export const fileOutDataUpload = (params?: WhTmLl | any) =>
  defHttp.get<WhTmLl>({
    url: adminPath + '/wh/ll/whTmLl/fileOutDataUpload',
    params,
  });

export const whTmLlHPosForm = (params?: WhTmLl | any) =>
  defHttp.get<WhTmLl>({ url: adminPath + '/wh/ll/whTmLl/posForm', params });

export const handleDataHb = (params?: WhTmLl | any) =>
  defHttp.post<WhTmLl>({ url: adminPath + '/wh/ll/whTmLl/hbLlData', params });

export const barCodeCheck = (params?: WhTmLl | any) =>
  defHttp.post<WhTmLl>({ url: adminPath + '/wh/ll/whTmLl/analyLl', params });

export const encLl = (params?: WhTmLl | any) =>
  defHttp.post<WhTmLl>({ url: adminPath + '/wh/ll/whTmLl/encLl', params });

export const batchDalete = (params?: WhTmLl | any) =>
  defHttp.post<WhTmLl>({ url: adminPath + '/wh/ll/whTmLl/batchDalete', params });

export const whTmLlImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/wh/ll/whTmLl/importData',
      onUploadProgress,
    },
    params,
  );
