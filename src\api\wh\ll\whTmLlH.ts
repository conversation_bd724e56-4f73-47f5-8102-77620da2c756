/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface WhTmLlH extends BasicModel<WhTmLlH> {
  djno?: string; // 申请单号
  cwhcode?: string; // 申请仓库
  xqcwhcode?: string; // 需求仓库
  cinvcode?: string; // 总成
  iqty?: number; // 数量
  whTmLlRdsList?: any[]; // 子表列表
}


export const listData = (params?: any) =>
  defHttp.post<WhTmLlH>({ url: adminPath + '/wh/ll/whTmLlH/masterListData', params });

export const whTmLlHList = (params?: WhTmLlH | any) =>
  defHttp.get<WhTmLlH>({ url: adminPath + '/wh/ll/whTmLlH/list', params });

export const whTmLlHListData = (params?: WhTmLlH | any) =>
  defHttp.post<Page<WhTmLlH>>({ url: adminPath + '/wh/ll/whTmLlH/listData', params });

export const whTmLlHForm = (params?: WhTmLlH | any) =>
  defHttp.get<WhTmLlH>({ url: adminPath + '/wh/ll/whTmLlH/form', params });

export const whTmLlHSave = (params?: any, data?: WhTmLlH | any) =>
  defHttp.postJson<WhTmLlH>({ url: adminPath + '/wh/ll/whTmLlH/save', params, data });

export const whTmLlHImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/wh/ll/whTmLlH/importData',
      onUploadProgress,
    },
    params,
  );

export const whTmLlHDelete = (params?: WhTmLlH | any) =>
  defHttp.get<WhTmLlH>({ url: adminPath + '/wh/ll/whTmLlH/delete', params });
