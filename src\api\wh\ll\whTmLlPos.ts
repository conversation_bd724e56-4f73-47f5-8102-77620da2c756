/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface WhTmLlPos extends BasicModel<WhTmLlPos> {
  did?: string; // did
  cposcode?: string; // 货位编码
  cbatch?: string; // 批次
  iqty?: number; // 数量
  bcheck?: string; // 是否下架
  no?: string; // 管理号
  retrace?: string; // 实集号
  sum?: string; // 位号
  ymdhms?: string; // 时间
}

export const whTmLlPosList = (params?: WhTmLlPos | any) =>
  defHttp.get<WhTmLlPos>({ url: adminPath + '/wh/ll/whTmLlPos/list', params });

export const whTmLlPosListData = (params?: WhTmLlPos | any) =>
  defHttp.post<Page<WhTmLlPos>>({ url: adminPath + '/wh/ll/whTmLlPos/listData', params });

export const whTmLlPosForm = (params?: WhTmLlPos | any) =>
  defHttp.get<WhTmLlPos>({ url: adminPath + '/wh/ll/whTmLlPos/form', params });

export const whTmLlPosSave = (params?: any, data?: WhTmLlPos | any) =>
  defHttp.postJson<WhTmLlPos>({ url: adminPath + '/wh/ll/whTmLlPos/save', params, data });

export const whTmLlPosDelete = (params?: WhTmLlPos | any) =>
  defHttp.get<WhTmLlPos>({ url: adminPath + '/wh/ll/whTmLlPos/delete', params });


export const bacthSave = (params?: any, data?: WhTmLlPos | any) =>
  defHttp.postJson<WhTmLlPos>({ url: adminPath + '/wh/ll/whTmLlPos/bacthSave', params, data });
