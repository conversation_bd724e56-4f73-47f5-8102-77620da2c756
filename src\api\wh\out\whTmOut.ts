/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface WhTmOut extends BasicModel<WhTmOut> {
  cinvcode?: string; // 物料编码
  cinvaddcode?: string; // 物料代码
  cinvname?: string; // 物料名称
  cwhcode?: string; // 仓库
  iqty?: number; // 下架数量
  printQty?: number; // 导出次数
}

export const whTmOutList = (params?: WhTmOut | any) =>
  defHttp.get<WhTmOut>({ url: adminPath + '/wh/out/whTmOut/list', params });

export const whTmOutListData = (params?: WhTmOut | any) =>
  defHttp.post<Page<WhTmOut>>({ url: adminPath + '/wh/out/whTmOut/listData', params });

export const whTmOutForm = (params?: WhTmOut | any) =>
  defHttp.get<WhTmOut>({ url: adminPath + '/wh/out/whTmOut/form', params });

export const whTmOutSave = (params?: any, data?: WhTmOut | any) =>
  defHttp.postJson<WhTmOut>({ url: adminPath + '/wh/out/whTmOut/save', params, data });

export const whTmOutDelete = (params?: WhTmOut | any) =>
  defHttp.get<WhTmOut>({ url: adminPath + '/wh/out/whTmOut/delete', params });

export const fileOutDataUpload = (params?: WhTmOut | any) =>
  defHttp.get<WhTmOut>({
    url: adminPath + '/wh/out/whTmOut/fileOutDataUpload',
    params,
  });
