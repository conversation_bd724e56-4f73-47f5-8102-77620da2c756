/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface WhTmSh extends BasicModel<WhTmSh> {
  cinvcode?: string; // 存货编码
  retrace?: string; // 追溯号
  sum?: string; // 流水号
  cbatch?: string; // 批次
  cwhcode?: string; // 仓库
  iqty?: number; // 数量
  sumQty?: number; // 累计上架数
  cpersoncode?: string; // 操作者
  cvencode?: string; // 供应商
  cposcode?: string; // 货位
}

export const whTmShList = (params?: WhTmSh | any) =>
  defHttp.get<WhTmSh>({ url: adminPath + '/wh/sh/whTmSh/list', params });

export const whTmShListData = (params?: WhTmSh | any) =>
  defHttp.post<Page<WhTmSh>>({ url: adminPath + '/wh/sh/whTmSh/listData', params });

export const whTmShForm = (params?: WhTmSh | any) =>
  defHttp.get<WhTmSh>({ url: adminPath + '/wh/sh/whTmSh/form', params });

export const whTmShSave = (params?: any, data?: WhTmSh | any) =>
  defHttp.postJson<WhTmSh>({ url: adminPath + '/wh/sh/whTmSh/save', params, data });

export const whTmShImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/wh/sh/whTmSh/importData',
      onUploadProgress,
    },
    params,
  );

export const whTmShDelete = (params?: WhTmSh | any) =>
  defHttp.get<WhTmSh>({ url: adminPath + '/wh/sh/whTmSh/delete', params });

export const whTmShSavePos = (params?: WhTmSh | any) =>
  defHttp.post<Page<WhTmSh>>({ url: adminPath + '/wh/sh/whTmSh/savePos', params });

export const whTmShHandleToU8 = (params?: WhTmSh | any) =>
  defHttp.get<WhTmSh>({ url: adminPath + '/wh/sh/whTmSh/handleToU8', params });
