/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface WhTmTl extends BasicModel<WhTmTl> {
  djno?: string; // 退料单号
  cinvcode?: string; // 物料编码
  iqty?: number; // 数量
  cwhcode?: string; // 仓库
  cbatch?: string; // 批次
  tlQty?: number; // 累计退料数
  tlBy?: string; // 退料人
  no?: string; // 唯一实际号
  sum?: string; // 流水号
  retrace?: string; // 追溯号
  ymdhms?: string; // 时间
  xppno?: string; // 现品票ID
  cposcode?: string; // 货位
  printQty?: number; // 打印数量
}

export const whTmTlList = (params?: WhTmTl | any) =>
  defHttp.get<WhTmTl>({ url: adminPath + '/wh/tl/whTmTl/list', params });

export const whTmTlListData = (params?: WhTmTl | any) =>
  defHttp.post<Page<WhTmTl>>({ url: adminPath + '/wh/tl/whTmTl/listData', params });

export const whTmTlForm = (params?: WhTmTl | any) =>
  defHttp.get<WhTmTl>({ url: adminPath + '/wh/tl/whTmTl/form', params });

export const whTmTlSave = (params?: any, data?: WhTmTl | any) =>
  defHttp.postJson<WhTmTl>({ url: adminPath + '/wh/tl/whTmTl/save', params, data });

export const whTmTlDelete = (params?: WhTmTl | any) =>
  defHttp.get<WhTmTl>({ url: adminPath + '/wh/tl/whTmTl/delete', params });

export const batchSave = (params?: any, data?: WhTmTl | any) =>
  defHttp.postJson<WhTmTl>({ url: adminPath + '/wh/tl/whTmTl/batchSave', params, data });

export const savePos = (params?: any, data?: WhTmTl | any) =>
  defHttp.postJson<WhTmTl>({ url: adminPath + '/wh/tl/whTmTl/savePos', params, data });

export const fileDataUpload = (params?: WhTmTl | any) =>
  defHttp.get<WhTmTl>({
    url: adminPath + '/wh/tl/whTmTl/fileDataUpload',
    params,
  });
