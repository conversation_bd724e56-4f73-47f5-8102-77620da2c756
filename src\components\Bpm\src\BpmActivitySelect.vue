<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicModal
    v-bind="$attrs"
    :title="t('节点选择')"
    :showOkBtn="false"
    :cancelText="t('common.closeText')"
    @register="registerModal"
    @cancel="_closeTip"
    :minHeight="200"
    width="80%"
  >
    <div class="jeesite-bpm-trace">
      <div class="box-model">
        <div class="box-tool" v-if="message">{{ message }}</div>
        <div ref="navTree" class="box-tool nav-tree" :data-text-main-process="t('主流程')"></div>
        <div ref="bpmnModel" style="overflow: auto"></div>
      </div>
      <div class="box-table">
        <FormGroup>{{ t('选择列表') }}</FormGroup>
        <BasicTable @register="registerTable" />
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'JeeSiteBpmRuntimeTrace',
    inheritAttrs: false,
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicTable, useTable } from '/@/components/Table';
  import { FormGroup } from '/@/components/Form';
  import {
    bpmTrace,
    bpmProcDefModel,
    bpmProcInsModel,
    bpmProcInsHistoryModel,
    bpmProcInsTrace,
  } from '/@/api/bpm';
  import { _showProcessDiagram, _closeTip } from './js/displaymodel.js';

  const emit = defineEmits(['update:selectData']);

  const { t } = useI18n('bpm.button');

  const message = ref();
  const historyProcInsId = ref('');
  const procInsId = ref('');
  const procDefId = ref('');
  const tableDataSource = ref<Recordable[]>([]);
  const selectEnabled = ref(false);

  const navTree = ref();
  const bpmnModel = ref();

  const [registerTable] = useTable({
    dataSource: tableDataSource,
    columns: [
      {
        title: t('环节名称'),
        dataIndex: 'name',
        width: 150,
        customRender: ({ record }) => {
          return record.name || t('未设置环节名');
        },
      },
      {
        title: t('创建时间'),
        dataIndex: 'createTime',
        width: 100,
      },
      {
        title: t('完成时间'),
        dataIndex: 'endTime',
        width: 100,
      },
      {
        title: t('任务历时'),
        dataIndex: 'durationFormat',
        width: 80,
      },
      {
        title: t('分配人员'),
        dataIndex: 'assigneeInfo',
        width: 150,
      },
      {
        title: t('审批意见'),
        dataIndex: 'comment',
        width: 200,
      },
    ],
    pagination: false,
    bordered: true,
    size: 'small',
    inset: true,
  });

  async function showProcessDiagram() {
    let data: any = {};
    if (historyProcInsId.value && historyProcInsId.value != '') {
      data = await bpmProcInsHistoryModel({ id: historyProcInsId.value });
      tableDataSource.value = await bpmProcInsTrace({ id: historyProcInsId.value });
    } else if (procInsId.value && procInsId.value != '') {
      data = await bpmProcInsModel({ id: procInsId.value });
      tableDataSource.value = await bpmProcInsTrace({ id: procInsId.value });
    } else if (procDefId.value && procDefId.value != '') {
      data = await bpmProcDefModel({ id: procDefId.value });
    }
    if (data.message) {
      message.value = data.message;
      return;
    }
    _showProcessDiagram(
      data,
      bpmnModel.value,
      navTree.value,
      tableDataSource.value,
      selectEnabled.value,
      emit,
    );
  }

  const [registerModal, { setModalProps }] = useModalInner(async (data = {}) => {
    setModalProps({ loading: true });
    if (data.formKey && data.formKey != '' && data.bizKey && data.bizKey != '') {
      const res = await bpmTrace({ formKey: data.formKey, bizKey: data.bizKey });
      data = res.params || {};
    }
    if (data.status == '1') {
      historyProcInsId.value = '';
      procInsId.value = data.id;
    } else {
      historyProcInsId.value = data.id;
      procInsId.value = '';
    }
    procDefId.value = data.procDef?.id;
    showProcessDiagram();
    setModalProps({ loading: false });
  });
</script>
<style lang="less">
  .jeesite-bpm-trace {
    position: relative;
    .box-model {
      text-align: center;
      .box-title {
        padding: 5px;
        margin-bottom: 5px;
        font-weight: bold;
        span {
          padding-right: 5px;
        }
      }
      .box-tool {
        margin-top: 20px;
      }
      .nav-tree a {
        white-space: normal;
        word-break: break-all;
      }
      svg {
        display: inline-block;
      }
    }
  }
</style>
