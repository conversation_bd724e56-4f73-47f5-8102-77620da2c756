<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div class="jeesite-bpm-btns">
    <div>
      <a-button type="default" @click="close">
        <Icon icon="ant-design:close-outlined" />{{ t('common.cancelText') }}
      </a-button>
    </div>
    <div v-if="traceText != '' && !isEmpty(taskRef?.procIns?.id)">
      <a-button type="primary" @click="trace">
        {{ t(traceText) }}
      </a-button>
    </div>
    <div
      v-if="
        getAuth &&
        needClaim &&
        backText != '' &&
        taskRef.procIns?.procDef?.form?.optionMap?.allowBackTask != '0'
      "
    >
      <a-button color="error" @click="back">
        {{ t(backText) }}
      </a-button>
    </div>
    <div
      v-if="
        getAuth &&
        needClaim &&
        turnText != '' &&
        taskRef.procIns?.procDef?.form?.optionMap?.allowTurnTask != '0'
      "
    >
      <a-button color="warning" @click="turn(false)">
        {{ t(turnText) }}
      </a-button>
    </div>
    <div
      v-if="
        getAuth &&
        needClaim &&
        delegateText != '' &&
        taskRef.procIns?.procDef?.form?.optionMap?.allowDelegateTask != '0'
      "
    >
      <a-button color="success" @click="turn(true)">
        {{ t(delegateText) }}
      </a-button>
    </div>
    <div
      v-if="
        getAuth &&
        needClaim &&
        taskRef.hasMultiInstance &&
        taskRef.procIns?.procDef?.form?.optionMap?.allowModifySignTask != '0'
      "
    >
      <a-button class="purple" @click="modifySign()">
        {{ t(modifySignText) }}
      </a-button>
    </div>
    <div
      v-if="
        getAuth &&
        needClaim &&
        stopText != '' &&
        (taskRef.procIns?.startUserId == userInfo.userCode || userInfo.userCode == 'system')
      "
    >
      <a-button color="error" @click="stop">
        {{ t(stopText) }}
      </a-button>
    </div>
    <div
      v-if="
        getAuth &&
        needClaim &&
        moveText != '' &&
        (userInfo.userCode == 'system' ||
          taskRef.procIns?.procDef?.form?.optionMap?.allowMoveTask == '1')
      "
    >
      <a-button color="warning" @click="move">
        {{ t(moveText) }}
      </a-button>
    </div>
    <div
      v-if="
        getAuth &&
        rollbackText != '' &&
        taskRef.procIns?.procDef?.form?.optionMap?.allowRollbackTask != '0' &&
        !isEmpty(taskRef.procIns?.id) &&
        (taskRef.procIns?.endTime == null || taskRef.procIns?.endTime == '') &&
        !isEmpty(taskRef.id) &&
        !(taskRef.endTime == null || taskRef.endTime == '') &&
        taskRef.assignee == userInfo.userCode
      "
    >
      <a-button color="error" @click="rollback">
        {{ t(rollbackText) }}
      </a-button>
    </div>
    <div v-if="getAuth && isInit && isEmpty(taskRef.procIns?.id) && draft">
      <a-button color="success" @click="completeDraft" :loading="loading">
        <Icon icon="ant-design:save-outlined" /> {{ t(draftText) }}
      </a-button>
    </div>
    <div v-if="getAuth && isInit && isEmpty(taskRef.procIns?.id)">
      <a-button type="primary" @click="complete" :loading="loading">
        <Icon icon="ant-design:check-outlined" /> {{ t(completeText) }}
      </a-button>
    </div>
    <div
      v-if="
        getAuth &&
        taskClaim &&
        (taskRef.assignee == null || taskRef.assignee == '') &&
        taskRef.assignee != userInfo.userCode
      "
    >
      <a-button color="success" @click="claim" :loading="loading">
        <Icon icon="ant-design:check-outlined" /> {{ t(claimText) }}
      </a-button>
    </div>
    <div v-if="getAuth && needClaim">
      <a-button type="primary" @click="complete" :loading="loading">
        <Icon icon="ant-design:check-outlined" /> {{ t(completeText) }}
      </a-button>
    </div>
    <BpmUserSelect @register="registerUserSelectModal" @select="userSelectOk" />
    <BpmTaskBack @register="registerBackModal" @success="success" />
    <BpmTaskTurn @register="registerTurnModal" @success="success" />
    <BpmTaskModifySign @register="registerModifySignModal" />
    <BpmRuntimeStop @register="registerStopModal" @success="success" />
    <BpmRuntimeTrace @register="registerTraceModal" />
  </div>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'JeeSiteBpmButton',
    inheritAttrs: false,
  });
</script>
<script lang="ts" setup>
  import { defineComponent, computed, watch, ref, defineEmits } from 'vue';
  import Icon from '/@/components/Icon/src/Icon.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useUserStore } from '/@/store/modules/user';
  import { useModal } from '/@/components/Modal';
  import { isEmpty } from '/@/utils/is';
  import { BpmTask, bpmGetTask, bpmClaim, bpmRollback, bpmGetNextUser } from '/@/api/bpm';
  import BpmUserSelect from './BpmUserSelect.vue';
  import BpmTaskBack from './BpmTaskBack.vue';
  import BpmTaskTurn from './BpmTaskTurn.vue';
  import BpmTaskModifySign from './BpmTaskModifySign.vue';
  import BpmRuntimeStop from './BpmRuntimeStop.vue';
  import BpmRuntimeTrace from './BpmRuntimeTrace.vue';

  const emit = defineEmits([
    // 'update:bpmEntity',
    'userSelect',
    'complete',
    'claim',
    'back',
    'turn',
    'delegate',
    'modifySign',
    'stop',
    'move',
    'trace',
    'rollback',
    'success',
    'close',
  ]);

  const { t } = useI18n('bpm.button');
  const { hasPermission } = usePermission();
  const { createConfirm, showMessageModal, showMessage } = useMessage();
  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;

  const props = defineProps({
    bpmEntity: {
      type: Object as PropType<Recordable>,
      default: () => {},
    },
    formKey: {
      type: String,
      default: '',
    },
    draft: {
      type: Boolean,
      default: true, // 是否启用草稿（草稿状态9，提交审核状态4）
    },
    claimText: {
      type: String,
      default: '签收',
    },
    draftText: {
      type: String,
      default: '草稿',
    },
    completeText: {
      type: String,
      default: '提交',
    },
    backText: {
      type: String,
      default: '退回', // 退回到之前办理过的任务环节
    },
    turnText: {
      type: String,
      default: '转办', // 将任务转交他人办理，办理完成后继续流转
    },
    delegateText: {
      type: String,
      default: '委托', // 任务交由被委托人办理，办理完成后返回委托人
    },
    modifySignText: {
      type: String,
      default: '加减签', // 任务加签或减签操作
    },
    stopText: {
      type: String,
      default: '终止', // 单据作废，结束流程实例
    },
    moveText: {
      type: String,
      default: '', // 特事特办，自由流，随意跳转
    },
    traceText: {
      type: String,
      default: '流程图', // 流程状态，流程图，流程记录
    },
    rollbackText: {
      type: String,
      default: '撤回任务', // 下一环节未审批的可撤回重做
    },
    loading: {
      type: Boolean as PropType<boolean>,
      default: false, // 按钮加载状态
    },
    auth: {
      type: String,
      default: '', // 按钮权限字符串
    },
    asyncEmits: {
      type: Array as PropType<string[]>,
      default: () => ['userSelect'],
    },
  });

  const getAuth = computed(() => {
    return hasPermission(props.auth);
  });

  const bizKeyRef = ref<String>('');
  const bpmEntity = ref<Recordable>({});
  const taskRef = ref<BpmTask>({});

  const isInit = ref(false);
  const taskClaim = ref(false);
  const needClaim = ref(false);

  const [registerUserSelectModal, { openModal: openUserSelectModal }] = useModal();
  const [registerBackModal, { openModal: backModel }] = useModal();
  const [registerTurnModal, { openModal: turnModel }] = useModal();
  const [registerModifySignModal, { openModal: modifySignModel }] = useModal();
  const [registerStopModal, { openModal: stopModel }] = useModal();
  const [registerTraceModal, { openModal: traceModel }] = useModal();

  watch(
    () => props.bpmEntity,
    () => {
      isInit.value = false;
      taskClaim.value = false;
      needClaim.value = false;
      const newId = props.bpmEntity['id'];
      const currId = props.bpmEntity['id'];
      const newBpm = props.bpmEntity['bpm'] || {};
      const currBpm = bpmEntity.value['bpm'] || {};
      if (!currId || newId != currId || !currBpm.taskId || newBpm.taskId != currBpm.taskId) {
        // console.log('id', newId, currId);
        // console.log('bpm', newBpm, currBpm);
        bizKeyRef.value = newId;
        bpmEntity.value = props.bpmEntity;
        loadData();
      }
    },
    { immediate: true },
  );

  async function loadData() {
    const currBpm = bpmEntity.value['bpm'] || {};
    const taskId = currBpm.taskId || '';
    // const procInsId = currBpm.procInsId || '';
    const formKey = props.formKey || '';
    const bizKey = bizKeyRef.value || '';
    if (taskId != '' || (formKey != '' && bizKey != '')) {
      const res = await bpmGetTask({
        id: taskId,
        'procIns.formKey': formKey,
        'procIns.bizKey': bizKey,
      });
      initialize(res);
    } else {
      initialize();
    }
    isInit.value = true;
  }

  function initialize(task: Recordable = {}) {
    const currBpm = bpmEntity.value['bpm'] || {};
    if (!task.id || task.id == '') {
      task.id = currBpm.taskId || '';
    }
    if (!task.procIns) {
      task.procIns = {};
    }
    if (!task.procIns.id || task.procIns.id == '') {
      task.procIns.id = currBpm.procInsId || '';
    }
    if (task.procIns.id == '') {
      taskClaim.value = false;
      needClaim.value = false;
    } else if (task.id != '' && (task.endTime == null || task.endTime == '')) {
      if (task.assignee == null || task.assignee == '') {
        taskClaim.value = true;
        needClaim.value = false;
      } else {
        taskClaim.value = false;
        needClaim.value = true;
      }
    }
    taskRef.value = task;
    // console.log('task', task);
    currBpm.taskId = task.id || '';
    currBpm.procInsId = task.procIns?.id || '';
    currBpm.activityId = task.activityId || '';
    currBpm.status = task.status;
    bpmEntity.value['bpm'] = currBpm;
    // console.log('bpm', bpmEntity.value);
    // emit('update:bpmEntity', bpmEntity.value);
  }

  function complete() {
    // 提交之前弹窗选择下一步处理人（根据业务需要启用）
    // emitPlus('userSelect', async (params) => {
    //   const res = await bpmGetNextUser(params);
    //   const { nextUsers, office } = res;
    //   const roleCodes: string[] = [];
    //   if (nextUsers) {
    //     nextUsers.forEach((e: Recordable) => {
    //       if (e.roleCodes) {
    //         roleCodes.push(e.roleCodes.join(','));
    //       }
    //     });
    //   }
    //   if (roleCodes.length > 0) {
    //     openUserSelectModal(true, {
    //       checkbox: false,
    //       roleCode: roleCodes.join(','),
    //       office,
    //     });
    //   } else {
    //     userSelectOk();
    //   }
    // });
    userSelectOk();
  }

  function userSelectOk(data?: Recordable) {
    if (data) {
      const userCodes: string[] = [];
      data.forEach((e) => {
        userCodes.push(e.userCode);
      });
      bpmEntity.value.bpm.nextUserCodes = userCodes.join(',');
    }
    bpmEntity.value.status = 4; // 状态：4:审核；9:草稿
    // emit('update:bpmEntity', bpmEntity.value);
    emitPlus('complete', (_res) => {
      // 有业务功能实现
    });
  }

  function completeDraft() {
    bpmEntity.value.status = 9; // 状态：4:审核；9:草稿
    // emit('update:bpmEntity', bpmEntity.value);
    emitPlus('complete', (_res) => {
      // 有业务功能实现
    });
  }

  function claim() {
    emitPlus('claim', async (_res) => {
      const res = await bpmClaim({ id: taskRef.value.id });
      showMessage(res.message);
      loadData();
    });
  }

  function back() {
    emitPlus('back', async (_res) => {
      backModel(true, { task: taskRef.value });
    });
  }

  function turn(turnDelegate) {
    emitPlus(turnDelegate ? 'delegate' : 'turn', (_res) => {
      turnModel(true, { task: taskRef.value, turnDelegate });
    });
  }

  function modifySign() {
    emitPlus('modifySign', async (_res) => {
      modifySignModel(true, { task: taskRef.value });
    });
  }

  function stop() {
    emitPlus('stop', (_res) => {
      stopModel(true, { procIns: taskRef.value.procIns });
    });
  }

  function move() {
    emitPlus('move', (_res) => {});
  }

  function trace() {
    emitPlus('trace', (_res) => {
      traceModel(true, taskRef.value.procIns);
    });
  }

  function rollback() {
    emitPlus('rollback', (_res) => {
      createConfirm({
        title: t('提示'),
        content: t(
          '如果该任务的下一环节未被处理，则可以尝试撤回该任务的办理。您确认要尝试撤回吗？',
        ),
        iconType: 'warning',
        onOk: async (_res) => {
          try {
            const res = await bpmRollback({ id: taskRef.value.id });
            showMessageModal({ content: res.message });
            success();
          } catch (error: any) {
            console.log('error', error);
          }
          return Promise.resolve();
        },
      });
    });
  }

  // 事件调用增强扩展，如果父组件没有实现该事件，则调用默认事件默认事件
  // 如果实现该事件，则第一行代码就调用 callback(false)，则终止默认事件
  // 举例，父组件实现事件 @complete="handleComplete" 该方法实现如下：
  // /**
  //  * 举例实现完成事件
  //  * @param data 调用 emitPlus 事件，第二个参数传递的数据
  //  * @param callback 调用 (defaultEvenCall, newData) 方法的函数
  //  * <AUTHOR>
  //  */
  // function handleComplete(data, callback) {
  //    const defaultEventCall = false; // 终止默认事件
  //    const defaultEventCall = true; // 继续执行默认事件
  //    const newData = {}; // 给默认事件传递的参数数据
  //    callback(defaultEventCall, newData);
  // }
  async function emitPlus(name, defaultEvent) {
    //console.log(name, taskRef.value)
    let res = true;
    let data = {};
    await emit(name, { task: taskRef.value }, (defaultEventCall, newData) => {
      if (defaultEventCall != undefined) {
        res = defaultEventCall;
      }
      if (newData != undefined) {
        data = newData;
      }
      if (res && props.asyncEmits.includes(name)) {
        defaultEvent(data);
      }
    });
    if (res && !props.asyncEmits.includes(name)) {
      defaultEvent(data);
    }
  }

  function success() {
    emit('success');
    emit('close');
  }

  function close() {
    emit('close');
  }
</script>
<style lang="less" scoped>
  .jeesite-bpm-btns {
    display: inline-block;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    width: 100%;
    height: 60px;
    > div {
      display: inline-block;
      padding-left: 8px;
    }
  }
  u-popup {
    display: none;
  }
  .form-title {
    font-size: 34rpx;
    padding: 28rpx 30rpx 10rpx;
  }
  .form-footer {
    margin: 0;
    padding: 0 10rpx;
    padding-bottom: 20rpx;
  }
</style>
