<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicModal
    v-bind="$attrs"
    :title="t('任务加减签')"
    @register="registerModal"
    @ok="handleSubmit"
    :minHeight="120"
    :width="700"
  >
    <BasicForm @register="registerForm">
      <template #signUserTags>
        <div class="sign-user-tags">
          <Tag v-for="(item, index) in signUserList" :key="item.assignee">
            <span :class="'tag-status-' + item.status">{{ item.assigneeName }}</span>
            <span class="tag-button" @click="toggleTag(item, index)">
              <Icon
                v-if="item.status !== '1'"
                icon="ant-design:close-outlined"
                :title="t('减签')"
              />
              <Icon v-else icon="ant-design:plus-outlined" :title="t('恢复')" />
            </span>
          </Tag>
        </div>
      </template>
      <template #addSignUserTags>
        <div class="sign-user-tags">
          <Tag v-for="(item, index) in selectListRef" :key="item.userCode">
            <span :class="'tag-status-' + item.status">{{ item.userName }}</span>
            <span class="tag-button" @click="removeTag(item, index)">
              <Icon icon="ant-design:close-outlined" :title="t('减签')" />
            </span>
          </Tag>
          <Tag class="tag-add-button" @click="addTag()">
            <Icon icon="ant-design:plus-outlined" :title="t('加签')" />
          </Tag>
        </div>
        <ListSelect
          ref="listSelectRef"
          selectType="empUserSelect"
          :checkbox="true"
          :selectList="selectListRef"
          @select="onSelect"
          v-show="false"
        />
      </template>
    </BasicForm>
  </BasicModal>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'JeeSiteBpmTaskBack',
    inheritAttrs: false,
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref } from 'vue';
  import { Tag } from 'ant-design-vue';
  import { Icon } from '/@/components/Icon';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { bpmModifySign, bpmModifySignTask } from '/@/api/bpm';
  import { ListSelect } from '/@/components/ListSelect';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('bpm.button');
  const { showMessage, showMessageModal } = useMessage();
  const record = ref<Recordable>({});

  const isSequential = ref<boolean>(false);
  const signUserList = ref<Recordable[]>([]);
  const selectListRef = ref<Recordable[]>([]);
  const listSelectRef = ref<any>(null);

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('当前环节'),
      field: 'name',
      component: 'Input',
      render: ({ values }) => {
        return (
          (values.name || t('未设置环节名')) + '（' + t(isSequential.value ? '串行' : '并行') + '）'
        );
      },
      required: true,
    },
    {
      label: t('当前会签'),
      field: 'signUserTags',
      component: 'Input',
      colProps: { lg: 23, md: 24 },
      slot: 'signUserTags',
    },
    {
      label: t('新加会签'),
      field: 'addSignUserTags',
      component: 'Input',
      colProps: { lg: 23, md: 24 },
      slot: 'addSignUserTags',
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 23, md: 24 },
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setModalProps({ loading: true });
    await resetFields();
    record.value = data.task || {};
    // console.log(record.value);
    setFieldsValue(record.value);
    const res = await bpmModifySign({ id: data.task.id });
    isSequential.value = res.isSequential || false;
    signUserList.value = res.signUserList || [];
    selectListRef.value = [];
    setModalProps({ loading: false });
  });

  const toggleTag = (item: Recordable, _index: number) => {
    item.status = item.status == '0' ? '1' : '0';
  };

  const addTag = () => {
    listSelectRef.value.openSelectModal();
  };

  const removeTag = (_item: Recordable, index: number) => {
    selectListRef.value.splice(index, 1);
  };

  const onSelect = (values: Recordable[]) => {
    selectListRef.value = values;
  };

  async function handleSubmit() {
    try {
      if (record.value.id == '') {
        showMessage(t('任务ID不能为空'));
        return;
      }
      const data = await validate();
      setModalProps({ confirmLoading: true });
      data.id = record.value.id;
      data.executionId = signUserList.value
        .filter((e) => e.status && e.status == '1')
        .map((e) => e.executionId)
        .join(',');
      data.assignee = selectListRef.value.map((e) => e.userCode).join(',');
      // console.log('submit', data);
      const res = await bpmModifySignTask(data);
      showMessageModal({ content: res.message });
      setTimeout(closeModal);
      emit('success');
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('您填写的信息有误，请根据提示修正。'));
      }
      console.log('error', error);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
<style lang="less">
  .sign-user-tags {
    .ant-tag {
      margin: 3px 5px 0 0 !important;
      padding: 2px 5px 2px 8px !important;
    }
    .tag-status-1 {
      text-decoration: line-through;
      color: #b86f6f;
    }
    .tag-button {
      cursor: pointer;
      padding-left: 5px;
      color: #666;
    }
    .tag-add-button {
      cursor: pointer;
      padding: 3px 8px !important;
    }
  }
</style>
