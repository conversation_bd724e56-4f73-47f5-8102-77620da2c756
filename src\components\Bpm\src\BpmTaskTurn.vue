<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicModal
    v-bind="$attrs"
    :title="t('任务' + getTurnDelegateText)"
    @register="registerModal"
    @ok="handleSubmit"
    :minHeight="120"
    :width="500"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'JeeSiteBpmTaskTurn',
    inheritAttrs: false,
  });
</script>
<script lang="ts" setup>
  import { defineComponent, computed, ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  // import { officeTreeData } from '/@/api/sys/office';
  import { bpmTurnTask } from '/@/api/bpm';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('bpm.button');
  const { showMessage, showMessageModal } = useMessage();
  const record = ref<Recordable>({});

  const turnDelegate = ref(false);
  const getTurnDelegateText = computed(() => {
    return turnDelegate.value ? '委托' : '转办';
  });

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('当前环节'),
      field: 'name',
      component: 'Input',
      render: ({ values }) => {
        return values.name || t('未设置环节名');
      },
      required: true,
    },
    {
      label: t(getTurnDelegateText.value + '人员'),
      field: 'userCode',
      component: 'ListSelect',
      componentProps: {
        // api: officeTreeData,
        // params: { isLoadUser: true, userIdPrefix: '' },
        // canSelectParent: false,
        // allowClear: true,
        selectType: 'empUserSelect',
      },
      required: true,
    },
    {
      label: t(getTurnDelegateText.value + '原因'),
      field: 'comment',
      component: 'InputTextArea',
      componentProps: {
        placeholder: t('请输入' + getTurnDelegateText.value + '原因'),
        maxlength: 500,
        rows: 3,
      },
    },
  ];

  const [registerForm, { updateSchema, resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 23, md: 24 },
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setModalProps({ loading: true });
    await resetFields();
    record.value = data.task || {};
    // console.log(record.value);
    turnDelegate.value = data.turnDelegate;
    updateSchema([
      {
        label: t(getTurnDelegateText.value + '人员'),
        field: 'userCode',
      },
      {
        label: t(getTurnDelegateText.value + '原因'),
        field: 'comment',
        componentProps: {
          placeholder: t('请输入' + getTurnDelegateText.value + '原因'),
        },
      },
    ]);
    setFieldsValue(record.value);
    setModalProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      if (record.value.id == '') {
        showMessage(t('任务ID不能为空'));
        return;
      }
      const data = await validate();
      setModalProps({ confirmLoading: true });
      data.id = record.value.id;
      data.delegateState = turnDelegate.value;
      console.log('submit', data);
      const res = await bpmTurnTask(data);
      showMessageModal({ content: res.message });
      setTimeout(closeModal);
      emit('success');
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('您填写的信息有误，请根据提示修正。'));
      }
      console.log('error', error);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
