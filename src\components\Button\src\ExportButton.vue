<template>
  <a-button type="default" @click="handleExport()">
    <Icon icon="ant-design:download-outlined" /> {{ t('导出') }}
  </a-button>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';
  export default defineComponent({
    name: 'ExportButton',
    inheritAttrs: false,
  });
</script>
<script lang="ts" setup>
  import { useGlobSetting } from '/@/hooks/setting';
  import { t } from '/@/hooks/web/useI18n';
  import { Icon } from '/@/components/Icon';
  import { downloadByUrl } from '/@/utils/file/download';
  const props = defineProps({
    api: String,
  });

  async function handleExport() {
    const { ctxAdminPath } = useGlobSetting();
    downloadByUrl({
      url: ctxAdminPath + props.api,
      target: '_self',
    });
  }
</script>
