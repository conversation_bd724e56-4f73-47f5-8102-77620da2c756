<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button type="default" @click="handleImport()">
          <Icon icon="ant-design:upload-outlined" /> {{ t('导入') }}
        </a-button>
        <a-button type="primary" @click="handleForm({})" v-auth="'wh:ll:whTmLlH:edit'">
          <Icon icon="fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ djno: record.djno })">
          {{ record.djno }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
    <FormImport @register="registerImportModal" @success="handleSuccess" />
  </div>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsWhLlWhTmLlHList',
  });
</script>
<script lang="ts" setup>
  import { defineComponent } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useGlobSetting } from '/@/hooks/setting';
  import { downloadByUrl } from '/@/utils/file/download';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { whTmLlHDelete, whTmLlHListData } from '/@/api/wh/ll/whTmLlH';
  import { useDrawer } from '/@/components/Drawer';
  import { useModal } from '/@/components/Modal';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';
  import FormImport from './formImport.vue';

  const { t } = useI18n('wh.ll.whTmLlH');
  const { showMessage } = useMessage();
  const getTitle = {
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: router.currentRoute.value.meta.title || t('领料申请'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('单据号'),
        field: 'djno',
        component: 'Input',
      },
      {
        label: t('物料代码'),
        field: 'basInv.cinvaddcode',
        component: 'Input',
      },
      {
        label: t('物料编码'),
        field: 'basInv.cinvcode',
        component: 'Input',
      },
      {
        label: t('类型'),
        field: 'area',
        component: 'Select',
        componentProps: {
          dictType: 'wh_bcp_area',
          allowClear: true,
          onChange: handleSuccess,
        },
      },
      {
        label: t('物料名称'),
        field: 'basInv.cinvname',
        component: 'Input',
      },
      {
        label: t('发料仓库'),
        field: 'cwhcode',
        component: 'Input',
      },
      {
        label: t('需求仓库'),
        field: 'xqcwhcode',
        component: 'Input',
      },
      {
        label: t('总成'),
        field: 'cinvcode',
        component: 'Input',
      },

    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('单据号'),
      dataIndex: 'djno',
      key: 'a.djno',
      sorter: true,
      width: 130,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('总成'),
      dataIndex: 'cinvcode',
      key: 'a.cinvcode',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('物料代码'),
      dataIndex: 'basInv.cinvaddcode',
      key: 'inv.cinvaddcode',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('物料名称'),
      dataIndex: 'basInv.cinvname',
      key: 'inv.cinvname',
      sorter: true,
      width: 130,
      align: 'left',
      ifShow: false,
    },
    {
      title: t('类型'),
      dataIndex: 'area',
      key: 'area',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('数量'),
      dataIndex: 'iqty',
      key: 'a.iqty',
      sorter: true,
      width: 130,
      align: 'right',
    },
    {
      title: t('需求日期'),
      dataIndex: 'xqDate',
      key: 'a.xqDate',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('状态'),
      dataIndex: 'llStatus',
      key: 'a.llStatus',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'ASD_LL_STATUS',
    },
    {
      title: t('备注'),
      dataIndex: 'remarks',
      key: 'a.remarks',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('发料仓库'),
      dataIndex: 'cwhcode',
      key: 'a.cwhcode',
      sorter: true,
      width: 230,
      align: 'left',
    },
    {
      title: t('需求仓库'),
      dataIndex: 'xqcwhcode',
      key: 'a.xqcwhcode',
      sorter: true,
      width: 130,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'clarity:note-edit-line',
        title: t('编辑领料申请'),
        onClick: handleForm.bind(this, { djno: record.djno }),
        auth: 'wh:ll:whTmLlH:edit',
      },
      {
        icon: 'ant-design:delete-outlined',
        color: 'error',
        title: t('删除领料申请'),
        popConfirm: {
          title: t('是否确认删除领料申请'),
          confirm: handleDelete.bind(this, { djno: record.djno }),
        },
        auth: 'wh:ll:whTmLlH:edit',
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload, getForm, setLoading }] = useTable({
    api: whTmLlHListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleExport() {
    const { ctxAdminPath } = useGlobSetting();
    downloadByUrl({
      url: ctxAdminPath + '/wh/ll/whTmLlH/exportData',
      target: '_self',
    });
  }

  const [registerImportModal, { openModal: importModal }] = useModal();

  function handleImport() {
    importModal(true, {});
  }

  async function handleDelete(record: Recordable) {
    setLoading(true);
    try {
      const res = await whTmLlHDelete(record);
      showMessage((res as any).message);
      handleSuccess();
    } finally {
      setLoading(false);
    }
  }

  function handleSuccess() {
    reload();
  }
</script>
